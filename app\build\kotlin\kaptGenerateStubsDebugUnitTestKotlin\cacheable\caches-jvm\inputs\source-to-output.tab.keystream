Kapp/src/test/java/com/example/castapp/integration/ScalingIntegrationTest.kt<app/src/test/java/com/example/castapp/CropPositionFixTest.ktBapp/src/test/java/com/example/castapp/AutoLayoutApplicationTest.kt<app/src/test/java/com/example/castapp/BatchWindowSyncTest.ktGapp/src/test/java/com/example/castapp/ui/DisconnectionSimulationTest.ktIapp/src/test/java/com/example/castapp/RemoteTextEditingIntegrationTest.kt@app/src/test/java/com/example/castapp/TextWindowSizeSync_Test.kt=app/src/test/java/com/example/castapp/ui/WindowRemovalTest.ktDapp/src/test/java/com/example/castapp/CornerRadiusConsistencyTest.ktCapp/src/test/java/com/example/castapp/TextWindowRotationSyncTest.kt8app/src/test/java/com/example/castapp/ExampleUnitTest.kt=app/src/test/java/com/example/castapp/PrecisionControlTest.kt>app/src/test/java/com/example/castapp/ResolutionManagerTest.ktJapp/src/test/java/com/example/castapp/service/AudioStreamingServiceTest.ktDapp/src/test/java/com/example/castapp/RemoteTextWindowManagerTest.ktFapp/src/test/java/com/example/castapp/utils/NotificationManagerTest.ktBapp/src/test/java/com/example/castapp/ui/view/WindowScalingTest.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             