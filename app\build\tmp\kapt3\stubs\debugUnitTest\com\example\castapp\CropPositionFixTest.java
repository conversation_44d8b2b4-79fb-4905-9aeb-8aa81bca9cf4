package com.example.castapp;

/**
 * 🎯 裁剪窗口位置同步修复测试
 *
 * 修复内容：
 * - 遥控端发送：容器位置坐标
 * - 接收端应用：直接应用到容器位置
 *
 * 这个修复解决了坐标系统不匹配的问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\b\u0010\u0005\u001a\u00020\u0004H\u0007\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/CropPositionFixTest;", "", "()V", "testCropPositionSync", "", "testDragScenario", "app_debugUnitTest"})
public final class CropPositionFixTest {
    
    public CropPositionFixTest() {
        super();
    }
    
    @org.junit.Test()
    public final void testCropPositionSync() {
    }
    
    @org.junit.Test()
    public final void testDragScenario() {
    }
}