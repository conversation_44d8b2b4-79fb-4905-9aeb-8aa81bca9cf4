/ Header Record For PersistentHashMapValueStoragez -com/example/castapp/AutoLayoutApplicationTest<com/example/castapp/AutoLayoutApplicationTest$MockLayoutItem.kotlin_module7 'com/example/castapp/BatchWindowSyncTest.kotlin_module? /com/example/castapp/CornerRadiusConsistencyTest.kotlin_module7 'com/example/castapp/CropPositionFixTest.kotlin_module3 #com/example/castapp/ExampleUnitTest.kotlin_module8 (com/example/castapp/PrecisionControlTest.kotlin_moduleD 4com/example/castapp/RemoteTextEditingIntegrationTest.kotlin_module? /com/example/castapp/RemoteTextWindowManagerTest.kotlin_module9 )com/example/castapp/ResolutionManagerTest.kotlin_module> .com/example/castapp/TextWindowRotationSyncTest.kotlin_module: *com/example/castapp/TextWindowSizeSyncTest.kotlin_moduleF 6com/example/castapp/integration/ScalingIntegrationTest.kotlin_moduleE 5com/example/castapp/service/AudioStreamingServiceTest.kotlin_moduleB 2com/example/castapp/ui/DisconnectionSimulationTest.kotlin_module8 (com/example/castapp/ui/WindowRemovalTest.kotlin_module= -com/example/castapp/ui/view/WindowScalingTest.kotlin_moduleA 1com/example/castapp/utils/NotificationManagerTest.kotlin_module