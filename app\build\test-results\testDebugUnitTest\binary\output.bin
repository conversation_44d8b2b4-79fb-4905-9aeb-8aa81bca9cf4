"🔄 测试空窗口数据处理
'✅ 空窗口数据处理测试通过
4🔄 测试包含裁剪数据的批量同步消息
9✅ 包含裁剪数据的批量同步消息测试通过
%🔄 测试批量同步消息创建
*✅ 批量同步消息创建测试通过

	+=== 圆角实现方式一致性测试 ===

	测试圆角半径: 16.5dp

	

	遥控端圆角实现:

	:  修复前: Canvas圆角 + Outline圆角 (双重效果)

	:    Canvas: canvas.drawRoundRect(..., 49.5, 49.5, paint)

	C    Outline: outline.setRoundRect(..., 49.5) + clipToOutline=true

	.  修复后: 仅Outline圆角 (单一效果)

	5    Canvas: canvas.drawRect(..., paint) (无圆角)

	C    Outline: outline.setRoundRect(..., 49.5) + clipToOutline=true

	

	接收端圆角实现:

	+  始终: 仅Outline圆角 (单一效果)

	C    Outline: outline.setRoundRect(..., 49.5) + clipToOutline=true

	

	一致性对比:

	I  修复前: 遥控端双重圆角 vs 接收端单一圆角 (不一致)

	L  修复后: 遥控端单一圆角 vs 接收端单一圆角 (完全一致)

	

	6✅ 圆角实现方式一致性修复验证通过！

1=== 缩放因子对圆角半径影响测试 ===

基础圆角半径: 16.0dp

*测试缩放因子: [0.5, 1.0, 1.5, 2.0]



缩放因子: 0.5x

9  接收端有效圆角: 16.0dp × 0.5 = 8.0dp (24.0px)

'  遥控端修复前: 16.0dp (48.0px)

6  遥控端修复后: 16.0dp × 0.5 = 8.0dp (24.0px)

  修复前差异: 24.0px

  修复后差异: 0.0px



缩放因子: 1.0x

:  接收端有效圆角: 16.0dp × 1.0 = 16.0dp (48.0px)

'  遥控端修复前: 16.0dp (48.0px)

7  遥控端修复后: 16.0dp × 1.0 = 16.0dp (48.0px)

  修复前差异: 0.0px

  修复后差异: 0.0px



缩放因子: 1.5x

:  接收端有效圆角: 16.0dp × 1.5 = 24.0dp (72.0px)

'  遥控端修复前: 16.0dp (48.0px)

7  遥控端修复后: 16.0dp × 1.5 = 24.0dp (72.0px)

  修复前差异: 24.0px

  修复后差异: 0.0px



缩放因子: 2.0x

:  接收端有效圆角: 16.0dp × 2.0 = 32.0dp (96.0px)

'  遥控端修复前: 16.0dp (48.0px)

7  遥控端修复后: 16.0dp × 2.0 = 32.0dp (96.0px)

  修复前差异: 48.0px

  修复后差异: 0.0px



<✅ 缩放因子对圆角半径影响修复验证通过！

.=== 多个圆角半径值一致性测试 ===

*测试值: [8.0, 12.5, 16.0, 20.3, 24.7]

模拟设备密度: 2.5



圆角半径: 8.0dp

  遥控端: 20.0 px

0  接收端修复前: 20.0 px (损失: 0.0 px)

0  接收端修复后: 20.0 px (差异: 0.0 px)



圆角半径: 12.5dp

  遥控端: 31.25 px

1  接收端修复前: 30.0 px (损失: 1.25 px)

1  接收端修复后: 31.25 px (差异: 0.0 px)



圆角半径: 16.0dp

  遥控端: 40.0 px

0  接收端修复前: 40.0 px (损失: 0.0 px)

0  接收端修复后: 40.0 px (差异: 0.0 px)



圆角半径: 20.3dp

  遥控端: 50.75 px

1  接收端修复前: 50.0 px (损失: 0.75 px)

1  接收端修复后: 50.75 px (差异: 0.0 px)



圆角半径: 24.7dp

  遥控端: 61.75 px

1  接收端修复前: 60.0 px (损失: 1.75 px)

1  接收端修复后: 61.75 px (差异: 0.0 px)



3✅ 多个圆角半径值一致性验证通过！


=== 边界情况测试 ===


:测试边界值: [0.0, 0.5, 1.0, 15.9, 16.0, 16.1, 32.0]





边界值: 0.0dp


  遥控端: 0.0 px


  接收端修复后: 0.0 px





边界值: 0.5dp


  遥控端: 1.5 px


  接收端修复后: 1.5 px





边界值: 1.0dp


  遥控端: 3.0 px


  接收端修复后: 3.0 px





边界值: 15.9dp


  遥控端: 47.699997 px


$  接收端修复后: 47.699997 px





边界值: 16.0dp


  遥控端: 48.0 px


  接收端修复后: 48.0 px





边界值: 16.1dp


  遥控端: 48.300003 px


$  接收端修复后: 48.300003 px





边界值: 32.0dp


  遥控端: 96.0 px


  接收端修复后: 96.0 px





!✅ 边界情况验证通过！

+=== 圆角半径计算一致性测试 ===

测试圆角半径: 16.5dp

模拟设备密度: 3.0



遥控端计算:

  dpToPx(16.5) = 49.5 px



接收端修复前:

  cornerRadius.toInt() = 16

"  dpToPx(16.toFloat()) = 48.0 px

  精度损失: 1.5 px



接收端修复后:

  dpToPxFloat(16.5) = 49.5 px

  与遥控端差异: 0.0 px



6✅ 圆角半径计算一致性修复验证通过！

"=== 精度损失场景分析 ===

+发现 450 个会造成精度损失的值

D前10个示例: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.1]

"最大精度损失: 2.6999998 px

'✅ 精度损失场景验证通过！
=== 拖动场景测试 ===
$初始容器位置: (200.0, 300.0)
*初始可见区域位置: (320.0, 380.0)
'初始可视化位置: (128.0, 152.0)

拖动操作:
  拖动距离: (20.0, 15.0)
&  新可视化位置: (148.0, 167.0)
)  新可见区域位置: (370.0, 417.5)
+  🎯 发送容器位置: (250.0, 337.5)

接收端处理:
&  接收容器位置: (250.0, 337.5)
'  🎯 修复：直接应用到容器

最终验证:
&  期望容器位置: (250.0, 337.5)
&  实际容器位置: (250.0, 337.5)
,  最终可见区域位置: (370.0, 417.5)
,  期望可见区域位置: (370.0, 417.5)
!✅ 拖动场景验证通过！
(=== 裁剪窗口位置同步测试 ===
容器位置: (100.0, 200.0)
$可见区域位置: (140.0, 320.0)
裁剪偏移: (40.0, 120.0)

遥控端发送:
   发送坐标: (100.0, 200.0)
$  🎯 修复：发送容器位置

接收端处理:
   接收坐标: (100.0, 200.0)
-  🎯 修复：直接应用到容器位置
   应用结果: (100.0, 200.0)

最终验证:
,  最终可见区域位置: (140.0, 320.0)
,  期望可见区域位置: (140.0, 320.0)
✅ 修复验证通过！
 ;WARNING: No manifest file found at .\AndroidManifest.xml.
 0Falling back to the Android OS resources only.
 VTo remove this warning, annotate your test class with @Config(manifest=Config.NONE).
  .No such manifest file: .\AndroidManifest.xml
