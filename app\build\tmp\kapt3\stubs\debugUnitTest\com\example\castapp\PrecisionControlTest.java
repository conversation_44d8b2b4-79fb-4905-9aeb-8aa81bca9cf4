package com.example.castapp;

/**
 * 精准控制功能测试
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\b\u0010\u0005\u001a\u00020\u0004H\u0007J\b\u0010\u0006\u001a\u00020\u0004H\u0007\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/PrecisionControlTest;", "", "()V", "testCastWindowInfoDefaultControlState", "", "testCastWindowInfoWithControlEnabled", "testTransformInfo", "app_debugUnitTest"})
public final class PrecisionControlTest {
    
    public PrecisionControlTest() {
        super();
    }
    
    @org.junit.Test()
    public final void testCastWindowInfoWithControlEnabled() {
    }
    
    @org.junit.Test()
    public final void testCastWindowInfoDefaultControlState() {
    }
    
    @org.junit.Test()
    public final void testTransformInfo() {
    }
}