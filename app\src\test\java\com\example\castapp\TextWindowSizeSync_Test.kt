package com.example.castapp

import org.junit.Test
import org.junit.Assert.*

/**
 * 文字窗口尺寸同步修复测试
 * 
 * 测试场景：
 * 1. 用户在遥控端调整文字窗口大小
 * 2. 关闭实时同步开关
 * 3. 点击同步按钮进行批量同步
 * 4. 验证接收端窗口尺寸是否正确
 */
class TextWindowSizeSyncTest {

    @Test
    fun testTextWindowSizeConversion() {
        // 模拟测试数据
        val remoteControlScale = 0.8483870967741935
        val originalSize = Pair(300, 200)  // 原始尺寸
        val userAdjustedSize = Pair(349, 253)  // 用户调整后的可视化尺寸
        
        // 修复前的错误计算（接收端会再次除以缩放因子）
        val wrongCalculation = Pair(
            (userAdjustedSize.first / remoteControlScale).toInt(),  // 349 ÷ 0.848 = 411
            (userAdjustedSize.second / remoteControlScale).toInt()   // 253 ÷ 0.848 = 298
        )
        
        // 修复后的正确计算（遥控端先转换，接收端直接应用）
        val correctCalculation = Pair(
            (userAdjustedSize.first / remoteControlScale).toInt(),  // 349 ÷ 0.848 = 411
            (userAdjustedSize.second / remoteControlScale).toInt()   // 253 ÷ 0.848 = 298
        )
        
        println("=== 文字窗口尺寸同步测试 ===")
        println("原始尺寸: ${originalSize.first}x${originalSize.second}")
        println("用户调整后的可视化尺寸: ${userAdjustedSize.first}x${userAdjustedSize.second}")
        println("远程控制缩放因子: $remoteControlScale")
        println()
        
        println("修复前（错误）:")
        println("  遥控端发送: ${userAdjustedSize.first}x${userAdjustedSize.second}")
        println("  接收端计算: ${userAdjustedSize.first}÷$remoteControlScale = ${(userAdjustedSize.first/remoteControlScale).toInt()}")
        println("  接收端计算: ${userAdjustedSize.second}÷$remoteControlScale = ${(userAdjustedSize.second/remoteControlScale).toInt()}")
        println("  最终结果: ${(userAdjustedSize.first/remoteControlScale).toInt()}x${(userAdjustedSize.second/remoteControlScale).toInt()}")
        println()
        
        println("修复后（正确）:")
        println("  遥控端转换: ${userAdjustedSize.first}÷$remoteControlScale = ${correctCalculation.first}")
        println("  遥控端转换: ${userAdjustedSize.second}÷$remoteControlScale = ${correctCalculation.second}")
        println("  遥控端发送: ${correctCalculation.first}x${correctCalculation.second}")
        println("  接收端直接应用: ${correctCalculation.first}x${correctCalculation.second}")
        println("  最终结果: ${correctCalculation.first}x${correctCalculation.second}")
        
        // 验证修复后的尺寸是合理的
        assertTrue("修复后的宽度应该大于原始宽度", correctCalculation.first > originalSize.first)
        assertTrue("修复后的高度应该大于原始高度", correctCalculation.second > originalSize.second)
        
        // 验证转换的合理性（用户调整的尺寸应该反映在最终结果中）
        val expectedWidth = (userAdjustedSize.first / remoteControlScale).toInt()
        val expectedHeight = (userAdjustedSize.second / remoteControlScale).toInt()
        
        assertEquals("宽度转换应该正确", expectedWidth, correctCalculation.first)
        assertEquals("高度转换应该正确", expectedHeight, correctCalculation.second)
    }
    
    @Test
    fun testSizeConversionWithDifferentScales() {
        val testCases = listOf(
            Triple(0.5, Pair(400, 300), Pair(800, 600)),    // 缩放0.5
            Triple(0.8, Pair(320, 240), Pair(400, 300)),    // 缩放0.8
            Triple(1.0, Pair(300, 200), Pair(300, 200)),    // 缩放1.0
            Triple(1.2, Pair(240, 160), Pair(200, 133))     // 缩放1.2
        )
        
        testCases.forEach { (scale, visualizedSize, expectedActualSize) ->
            val actualSize = Pair(
                (visualizedSize.first / scale).toInt(),
                (visualizedSize.second / scale).toInt()
            )
            
            println("缩放因子: $scale")
            println("  可视化尺寸: ${visualizedSize.first}x${visualizedSize.second}")
            println("  实际尺寸: ${actualSize.first}x${actualSize.second}")
            println("  期望尺寸: ${expectedActualSize.first}x${expectedActualSize.second}")
            
            assertEquals("宽度转换应该正确", expectedActualSize.first, actualSize.first)
            // 高度可能有1像素的舍入误差，允许±1的差异
            assertTrue("高度转换应该基本正确", 
                Math.abs(expectedActualSize.second - actualSize.second) <= 1)
        }
    }
}
