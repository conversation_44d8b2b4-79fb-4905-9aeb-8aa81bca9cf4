  Application android.app  Context android.content  
TypedValue android.util  Gravity android.view  CENTER android.view.Gravity  Any com.example.castapp  AutoLayoutApplicationTest com.example.castapp  BatchWindowSyncTest com.example.castapp  Boolean com.example.castapp  CastWindowInfo com.example.castapp  ControlMessage com.example.castapp  CornerRadiusConsistencyTest com.example.castapp  CropPositionFixTest com.example.castapp  ExampleUnitTest com.example.castapp  	Exception com.example.castapp  Float com.example.castapp  Int com.example.castapp  List com.example.castapp  Map com.example.castapp  Math com.example.castapp  MockLayoutItem com.example.castapp  Pair com.example.castapp  PrecisionControlTest com.example.castapp  RemoteReceiverConnection com.example.castapp   RemoteTextEditingIntegrationTest com.example.castapp  RemoteTextWindowManager com.example.castapp  RemoteTextWindowManagerTest com.example.castapp  ResolutionManagerTest com.example.castapp  RobolectricTestRunner com.example.castapp  RuntimeEnvironment com.example.castapp  String com.example.castapp  Suppress com.example.castapp  TextWindowRotationSyncTest com.example.castapp  TextWindowSizeSyncTest com.example.castapp  Triple com.example.castapp  android com.example.castapp  arrayOf com.example.castapp  assert com.example.castapp  assertEquals com.example.castapp  assertFalse com.example.castapp  assertNotEquals com.example.castapp  
assertTrue com.example.castapp  coerceIn com.example.castapp  com com.example.castapp  contains com.example.castapp  contentToString com.example.castapp  	emptyList com.example.castapp  forEach com.example.castapp  
isNotEmpty com.example.castapp  listOf com.example.castapp  mapOf com.example.castapp  
mutableListOf com.example.castapp  println com.example.castapp  
roundToInt com.example.castapp  
startsWith com.example.castapp  take com.example.castapp  to com.example.castapp  Boolean -com.example.castapp.AutoLayoutApplicationTest  Float -com.example.castapp.AutoLayoutApplicationTest  MockLayoutItem -com.example.castapp.AutoLayoutApplicationTest  Pair -com.example.castapp.AutoLayoutApplicationTest  String -com.example.castapp.AutoLayoutApplicationTest  Test -com.example.castapp.AutoLayoutApplicationTest  assertEquals -com.example.castapp.AutoLayoutApplicationTest  
assertTrue -com.example.castapp.AutoLayoutApplicationTest  createMockLayoutItem -com.example.castapp.AutoLayoutApplicationTest  createMockLayoutItemWithCrop -com.example.castapp.AutoLayoutApplicationTest  getASSERTEquals -com.example.castapp.AutoLayoutApplicationTest  
getASSERTTrue -com.example.castapp.AutoLayoutApplicationTest  getAssertEquals -com.example.castapp.AutoLayoutApplicationTest  
getAssertTrue -com.example.castapp.AutoLayoutApplicationTest  Boolean <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  Float <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  String <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  cropRect <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  	positionX <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  	positionY <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  
rotationAngle <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  scaleFactor <com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem  Any 'com.example.castapp.BatchWindowSyncTest  ControlMessage 'com.example.castapp.BatchWindowSyncTest  	Exception 'com.example.castapp.BatchWindowSyncTest  List 'com.example.castapp.BatchWindowSyncTest  Map 'com.example.castapp.BatchWindowSyncTest  String 'com.example.castapp.BatchWindowSyncTest  Suppress 'com.example.castapp.BatchWindowSyncTest  Test 'com.example.castapp.BatchWindowSyncTest  assertEquals 'com.example.castapp.BatchWindowSyncTest  
assertTrue 'com.example.castapp.BatchWindowSyncTest  	emptyList 'com.example.castapp.BatchWindowSyncTest  getASSERTEquals 'com.example.castapp.BatchWindowSyncTest  
getASSERTTrue 'com.example.castapp.BatchWindowSyncTest  getAssertEquals 'com.example.castapp.BatchWindowSyncTest  
getAssertTrue 'com.example.castapp.BatchWindowSyncTest  getEMPTYList 'com.example.castapp.BatchWindowSyncTest  getEmptyList 'com.example.castapp.BatchWindowSyncTest  	getLISTOf 'com.example.castapp.BatchWindowSyncTest  	getListOf 'com.example.castapp.BatchWindowSyncTest  getMAPOf 'com.example.castapp.BatchWindowSyncTest  getMapOf 'com.example.castapp.BatchWindowSyncTest  
getPRINTLN 'com.example.castapp.BatchWindowSyncTest  
getPrintln 'com.example.castapp.BatchWindowSyncTest  getTO 'com.example.castapp.BatchWindowSyncTest  getTo 'com.example.castapp.BatchWindowSyncTest  listOf 'com.example.castapp.BatchWindowSyncTest  mapOf 'com.example.castapp.BatchWindowSyncTest  println 'com.example.castapp.BatchWindowSyncTest  to 'com.example.castapp.BatchWindowSyncTest  Float /com.example.castapp.CornerRadiusConsistencyTest  Math /com.example.castapp.CornerRadiusConsistencyTest  Test /com.example.castapp.CornerRadiusConsistencyTest  arrayOf /com.example.castapp.CornerRadiusConsistencyTest  assertEquals /com.example.castapp.CornerRadiusConsistencyTest  assertNotEquals /com.example.castapp.CornerRadiusConsistencyTest  
assertTrue /com.example.castapp.CornerRadiusConsistencyTest  contentToString /com.example.castapp.CornerRadiusConsistencyTest  
getARRAYOf /com.example.castapp.CornerRadiusConsistencyTest  getASSERTEquals /com.example.castapp.CornerRadiusConsistencyTest  getASSERTNotEquals /com.example.castapp.CornerRadiusConsistencyTest  
getASSERTTrue /com.example.castapp.CornerRadiusConsistencyTest  
getArrayOf /com.example.castapp.CornerRadiusConsistencyTest  getAssertEquals /com.example.castapp.CornerRadiusConsistencyTest  getAssertNotEquals /com.example.castapp.CornerRadiusConsistencyTest  
getAssertTrue /com.example.castapp.CornerRadiusConsistencyTest  getCONTENTToString /com.example.castapp.CornerRadiusConsistencyTest  getContentToString /com.example.castapp.CornerRadiusConsistencyTest  getMUTABLEListOf /com.example.castapp.CornerRadiusConsistencyTest  getMutableListOf /com.example.castapp.CornerRadiusConsistencyTest  
getPRINTLN /com.example.castapp.CornerRadiusConsistencyTest  
getPrintln /com.example.castapp.CornerRadiusConsistencyTest  getTAKE /com.example.castapp.CornerRadiusConsistencyTest  getTake /com.example.castapp.CornerRadiusConsistencyTest  
mockDpToPx /com.example.castapp.CornerRadiusConsistencyTest  
mutableListOf /com.example.castapp.CornerRadiusConsistencyTest  println /com.example.castapp.CornerRadiusConsistencyTest  take /com.example.castapp.CornerRadiusConsistencyTest  Test 'com.example.castapp.CropPositionFixTest  assertEquals 'com.example.castapp.CropPositionFixTest  getASSERTEquals 'com.example.castapp.CropPositionFixTest  getAssertEquals 'com.example.castapp.CropPositionFixTest  
getPRINTLN 'com.example.castapp.CropPositionFixTest  
getPrintln 'com.example.castapp.CropPositionFixTest  println 'com.example.castapp.CropPositionFixTest  Test #com.example.castapp.ExampleUnitTest  assertEquals #com.example.castapp.ExampleUnitTest  getASSERTEquals #com.example.castapp.ExampleUnitTest  getAssertEquals #com.example.castapp.ExampleUnitTest  CastWindowInfo (com.example.castapp.PrecisionControlTest  Test (com.example.castapp.PrecisionControlTest  assertEquals (com.example.castapp.PrecisionControlTest  assertFalse (com.example.castapp.PrecisionControlTest  
assertTrue (com.example.castapp.PrecisionControlTest  contains (com.example.castapp.PrecisionControlTest  getASSERTEquals (com.example.castapp.PrecisionControlTest  getASSERTFalse (com.example.castapp.PrecisionControlTest  
getASSERTTrue (com.example.castapp.PrecisionControlTest  getAssertEquals (com.example.castapp.PrecisionControlTest  getAssertFalse (com.example.castapp.PrecisionControlTest  
getAssertTrue (com.example.castapp.PrecisionControlTest  getCONTAINS (com.example.castapp.PrecisionControlTest  getContains (com.example.castapp.PrecisionControlTest  Before 4com.example.castapp.RemoteTextEditingIntegrationTest  Context 4com.example.castapp.RemoteTextEditingIntegrationTest  ControlMessage 4com.example.castapp.RemoteTextEditingIntegrationTest  	Exception 4com.example.castapp.RemoteTextEditingIntegrationTest  RemoteReceiverConnection 4com.example.castapp.RemoteTextEditingIntegrationTest  RuntimeEnvironment 4com.example.castapp.RemoteTextEditingIntegrationTest  Test 4com.example.castapp.RemoteTextEditingIntegrationTest  android 4com.example.castapp.RemoteTextEditingIntegrationTest  assert 4com.example.castapp.RemoteTextEditingIntegrationTest  com 4com.example.castapp.RemoteTextEditingIntegrationTest  context 4com.example.castapp.RemoteTextEditingIntegrationTest  
getANDROID 4com.example.castapp.RemoteTextEditingIntegrationTest  	getASSERT 4com.example.castapp.RemoteTextEditingIntegrationTest  
getAndroid 4com.example.castapp.RemoteTextEditingIntegrationTest  	getAssert 4com.example.castapp.RemoteTextEditingIntegrationTest  getCOM 4com.example.castapp.RemoteTextEditingIntegrationTest  getCom 4com.example.castapp.RemoteTextEditingIntegrationTest  
getISNotEmpty 4com.example.castapp.RemoteTextEditingIntegrationTest  
getIsNotEmpty 4com.example.castapp.RemoteTextEditingIntegrationTest  getMAPOf 4com.example.castapp.RemoteTextEditingIntegrationTest  getMapOf 4com.example.castapp.RemoteTextEditingIntegrationTest  
getPRINTLN 4com.example.castapp.RemoteTextEditingIntegrationTest  
getPrintln 4com.example.castapp.RemoteTextEditingIntegrationTest  getTO 4com.example.castapp.RemoteTextEditingIntegrationTest  getTo 4com.example.castapp.RemoteTextEditingIntegrationTest  invoke 4com.example.castapp.RemoteTextEditingIntegrationTest  
isNotEmpty 4com.example.castapp.RemoteTextEditingIntegrationTest  mapOf 4com.example.castapp.RemoteTextEditingIntegrationTest  println 4com.example.castapp.RemoteTextEditingIntegrationTest  remoteReceiverConnection 4com.example.castapp.RemoteTextEditingIntegrationTest  to 4com.example.castapp.RemoteTextEditingIntegrationTest  Before /com.example.castapp.RemoteTextWindowManagerTest  Context /com.example.castapp.RemoteTextWindowManagerTest  	Exception /com.example.castapp.RemoteTextWindowManagerTest  RemoteReceiverConnection /com.example.castapp.RemoteTextWindowManagerTest  RemoteTextWindowManager /com.example.castapp.RemoteTextWindowManagerTest  RuntimeEnvironment /com.example.castapp.RemoteTextWindowManagerTest  Test /com.example.castapp.RemoteTextWindowManagerTest  assert /com.example.castapp.RemoteTextWindowManagerTest  	getASSERT /com.example.castapp.RemoteTextWindowManagerTest  	getAssert /com.example.castapp.RemoteTextWindowManagerTest  
getPRINTLN /com.example.castapp.RemoteTextWindowManagerTest  
getPrintln /com.example.castapp.RemoteTextWindowManagerTest  invoke /com.example.castapp.RemoteTextWindowManagerTest  println /com.example.castapp.RemoteTextWindowManagerTest  remoteReceiverConnection /com.example.castapp.RemoteTextWindowManagerTest  remoteTextWindowManager /com.example.castapp.RemoteTextWindowManagerTest  Boolean )com.example.castapp.ResolutionManagerTest  Int )com.example.castapp.ResolutionManagerTest  Pair )com.example.castapp.ResolutionManagerTest  Test )com.example.castapp.ResolutionManagerTest  assertEquals )com.example.castapp.ResolutionManagerTest  assertFalse )com.example.castapp.ResolutionManagerTest  assertNotEquals )com.example.castapp.ResolutionManagerTest  
assertTrue )com.example.castapp.ResolutionManagerTest  calculateScaledResolution )com.example.castapp.ResolutionManagerTest  calculateWindowSize )com.example.castapp.ResolutionManagerTest  getASSERTEquals )com.example.castapp.ResolutionManagerTest  getASSERTFalse )com.example.castapp.ResolutionManagerTest  getASSERTNotEquals )com.example.castapp.ResolutionManagerTest  
getASSERTTrue )com.example.castapp.ResolutionManagerTest  getAssertEquals )com.example.castapp.ResolutionManagerTest  getAssertFalse )com.example.castapp.ResolutionManagerTest  getAssertNotEquals )com.example.castapp.ResolutionManagerTest  
getAssertTrue )com.example.castapp.ResolutionManagerTest  	getLISTOf )com.example.castapp.ResolutionManagerTest  	getListOf )com.example.castapp.ResolutionManagerTest  
getPRINTLN )com.example.castapp.ResolutionManagerTest  
getPrintln )com.example.castapp.ResolutionManagerTest  
getROUNDToInt )com.example.castapp.ResolutionManagerTest  
getRoundToInt )com.example.castapp.ResolutionManagerTest  isValidResolution )com.example.castapp.ResolutionManagerTest  isValidScale )com.example.castapp.ResolutionManagerTest  listOf )com.example.castapp.ResolutionManagerTest  println )com.example.castapp.ResolutionManagerTest  
roundToInt )com.example.castapp.ResolutionManagerTest  Float .com.example.castapp.TextWindowRotationSyncTest  Pair .com.example.castapp.TextWindowRotationSyncTest  Test .com.example.castapp.TextWindowRotationSyncTest  assertEquals .com.example.castapp.TextWindowRotationSyncTest  assertFalse .com.example.castapp.TextWindowRotationSyncTest  assertNotEquals .com.example.castapp.TextWindowRotationSyncTest  
assertTrue .com.example.castapp.TextWindowRotationSyncTest  coerceIn .com.example.castapp.TextWindowRotationSyncTest  getASSERTEquals .com.example.castapp.TextWindowRotationSyncTest  getASSERTFalse .com.example.castapp.TextWindowRotationSyncTest  getASSERTNotEquals .com.example.castapp.TextWindowRotationSyncTest  
getASSERTTrue .com.example.castapp.TextWindowRotationSyncTest  getAssertEquals .com.example.castapp.TextWindowRotationSyncTest  getAssertFalse .com.example.castapp.TextWindowRotationSyncTest  getAssertNotEquals .com.example.castapp.TextWindowRotationSyncTest  
getAssertTrue .com.example.castapp.TextWindowRotationSyncTest  getCOERCEIn .com.example.castapp.TextWindowRotationSyncTest  getCoerceIn .com.example.castapp.TextWindowRotationSyncTest  	getLISTOf .com.example.castapp.TextWindowRotationSyncTest  	getListOf .com.example.castapp.TextWindowRotationSyncTest  
getPRINTLN .com.example.castapp.TextWindowRotationSyncTest  
getPrintln .com.example.castapp.TextWindowRotationSyncTest  
getSTARTSWith .com.example.castapp.TextWindowRotationSyncTest  
getStartsWith .com.example.castapp.TextWindowRotationSyncTest  getTO .com.example.castapp.TextWindowRotationSyncTest  getTo .com.example.castapp.TextWindowRotationSyncTest  listOf .com.example.castapp.TextWindowRotationSyncTest  println .com.example.castapp.TextWindowRotationSyncTest  
startsWith .com.example.castapp.TextWindowRotationSyncTest  to .com.example.castapp.TextWindowRotationSyncTest  Math *com.example.castapp.TextWindowSizeSyncTest  Pair *com.example.castapp.TextWindowSizeSyncTest  Test *com.example.castapp.TextWindowSizeSyncTest  Triple *com.example.castapp.TextWindowSizeSyncTest  assertEquals *com.example.castapp.TextWindowSizeSyncTest  
assertTrue *com.example.castapp.TextWindowSizeSyncTest  getASSERTEquals *com.example.castapp.TextWindowSizeSyncTest  
getASSERTTrue *com.example.castapp.TextWindowSizeSyncTest  getAssertEquals *com.example.castapp.TextWindowSizeSyncTest  
getAssertTrue *com.example.castapp.TextWindowSizeSyncTest  	getLISTOf *com.example.castapp.TextWindowSizeSyncTest  	getListOf *com.example.castapp.TextWindowSizeSyncTest  
getPRINTLN *com.example.castapp.TextWindowSizeSyncTest  
getPrintln *com.example.castapp.TextWindowSizeSyncTest  listOf *com.example.castapp.TextWindowSizeSyncTest  println *com.example.castapp.TextWindowSizeSyncTest  CastWindowInfo com.example.castapp.model  
Connection com.example.castapp.model  RemoteReceiverConnection com.example.castapp.model  connectionId (com.example.castapp.model.CastWindowInfo  getTransformInfo (com.example.castapp.model.CastWindowInfo  isControlEnabled (com.example.castapp.model.CastWindowInfo  connectionId $com.example.castapp.model.Connection  create $com.example.castapp.model.Connection  generateConnectionId $com.example.castapp.model.Connection  getDisplayText $com.example.castapp.model.Connection  	ipAddress $com.example.castapp.model.Connection  port $com.example.castapp.model.Connection  create .com.example.castapp.model.Connection.Companion  generateConnectionId .com.example.castapp.model.Connection.Companion  invoke .com.example.castapp.model.Connection.Companion  
deviceName 2com.example.castapp.model.RemoteReceiverConnection  id 2com.example.castapp.model.RemoteReceiverConnection  	ipAddress 2com.example.castapp.model.RemoteReceiverConnection  isConnected 2com.example.castapp.model.RemoteReceiverConnection  port 2com.example.castapp.model.RemoteReceiverConnection  invoke <com.example.castapp.model.RemoteReceiverConnection.Companion  AudioStreamingServiceTest com.example.castapp.service  Int com.example.castapp.service  Pair com.example.castapp.service  String com.example.castapp.service  assertEquals com.example.castapp.service  
assertTrue com.example.castapp.service  forEach com.example.castapp.service  listOf com.example.castapp.service  to com.example.castapp.service  Int 5com.example.castapp.service.AudioStreamingServiceTest  Pair 5com.example.castapp.service.AudioStreamingServiceTest  String 5com.example.castapp.service.AudioStreamingServiceTest  Test 5com.example.castapp.service.AudioStreamingServiceTest  assertEquals 5com.example.castapp.service.AudioStreamingServiceTest  
assertTrue 5com.example.castapp.service.AudioStreamingServiceTest  getASSERTEquals 5com.example.castapp.service.AudioStreamingServiceTest  
getASSERTTrue 5com.example.castapp.service.AudioStreamingServiceTest  getAssertEquals 5com.example.castapp.service.AudioStreamingServiceTest  
getAssertTrue 5com.example.castapp.service.AudioStreamingServiceTest  	getLISTOf 5com.example.castapp.service.AudioStreamingServiceTest  	getListOf 5com.example.castapp.service.AudioStreamingServiceTest  getTO 5com.example.castapp.service.AudioStreamingServiceTest  getTo 5com.example.castapp.service.AudioStreamingServiceTest  listOf 5com.example.castapp.service.AudioStreamingServiceTest  to 5com.example.castapp.service.AudioStreamingServiceTest  
Connection com.example.castapp.ui  ControlMessage com.example.castapp.ui  DisconnectionSimulationTest com.example.castapp.ui  String com.example.castapp.ui  WindowRemovalTest com.example.castapp.ui  assertEquals com.example.castapp.ui  assertFalse com.example.castapp.ui  assertNotEquals com.example.castapp.ui  
assertTrue com.example.castapp.ui  forEach com.example.castapp.ui  isBlank com.example.castapp.ui  
isNotBlank com.example.castapp.ui  
mutableListOf com.example.castapp.ui  mutableMapOf com.example.castapp.ui  mutableSetOf com.example.castapp.ui  repeat com.example.castapp.ui  set com.example.castapp.ui  
Connection 2com.example.castapp.ui.DisconnectionSimulationTest  ControlMessage 2com.example.castapp.ui.DisconnectionSimulationTest  String 2com.example.castapp.ui.DisconnectionSimulationTest  Test 2com.example.castapp.ui.DisconnectionSimulationTest  assertEquals 2com.example.castapp.ui.DisconnectionSimulationTest  
assertTrue 2com.example.castapp.ui.DisconnectionSimulationTest  getASSERTEquals 2com.example.castapp.ui.DisconnectionSimulationTest  
getASSERTTrue 2com.example.castapp.ui.DisconnectionSimulationTest  getAssertEquals 2com.example.castapp.ui.DisconnectionSimulationTest  
getAssertTrue 2com.example.castapp.ui.DisconnectionSimulationTest  
getISNotBlank 2com.example.castapp.ui.DisconnectionSimulationTest  
getIsNotBlank 2com.example.castapp.ui.DisconnectionSimulationTest  getMUTABLEListOf 2com.example.castapp.ui.DisconnectionSimulationTest  getMUTABLEMapOf 2com.example.castapp.ui.DisconnectionSimulationTest  getMUTABLESetOf 2com.example.castapp.ui.DisconnectionSimulationTest  getMutableListOf 2com.example.castapp.ui.DisconnectionSimulationTest  getMutableMapOf 2com.example.castapp.ui.DisconnectionSimulationTest  getMutableSetOf 2com.example.castapp.ui.DisconnectionSimulationTest  	getREPEAT 2com.example.castapp.ui.DisconnectionSimulationTest  	getRepeat 2com.example.castapp.ui.DisconnectionSimulationTest  getSET 2com.example.castapp.ui.DisconnectionSimulationTest  getSet 2com.example.castapp.ui.DisconnectionSimulationTest  
isNotBlank 2com.example.castapp.ui.DisconnectionSimulationTest  
mutableListOf 2com.example.castapp.ui.DisconnectionSimulationTest  mutableMapOf 2com.example.castapp.ui.DisconnectionSimulationTest  mutableSetOf 2com.example.castapp.ui.DisconnectionSimulationTest  repeat 2com.example.castapp.ui.DisconnectionSimulationTest  set 2com.example.castapp.ui.DisconnectionSimulationTest  
Connection (com.example.castapp.ui.WindowRemovalTest  ControlMessage (com.example.castapp.ui.WindowRemovalTest  String (com.example.castapp.ui.WindowRemovalTest  Test (com.example.castapp.ui.WindowRemovalTest  assertEquals (com.example.castapp.ui.WindowRemovalTest  assertFalse (com.example.castapp.ui.WindowRemovalTest  assertNotEquals (com.example.castapp.ui.WindowRemovalTest  
assertTrue (com.example.castapp.ui.WindowRemovalTest  getASSERTEquals (com.example.castapp.ui.WindowRemovalTest  getASSERTFalse (com.example.castapp.ui.WindowRemovalTest  getASSERTNotEquals (com.example.castapp.ui.WindowRemovalTest  
getASSERTTrue (com.example.castapp.ui.WindowRemovalTest  getAssertEquals (com.example.castapp.ui.WindowRemovalTest  getAssertFalse (com.example.castapp.ui.WindowRemovalTest  getAssertNotEquals (com.example.castapp.ui.WindowRemovalTest  
getAssertTrue (com.example.castapp.ui.WindowRemovalTest  
getISBlank (com.example.castapp.ui.WindowRemovalTest  
getISNotBlank (com.example.castapp.ui.WindowRemovalTest  
getIsBlank (com.example.castapp.ui.WindowRemovalTest  
getIsNotBlank (com.example.castapp.ui.WindowRemovalTest  getMUTABLEMapOf (com.example.castapp.ui.WindowRemovalTest  getMUTABLESetOf (com.example.castapp.ui.WindowRemovalTest  getMutableMapOf (com.example.castapp.ui.WindowRemovalTest  getMutableSetOf (com.example.castapp.ui.WindowRemovalTest  	getREPEAT (com.example.castapp.ui.WindowRemovalTest  	getRepeat (com.example.castapp.ui.WindowRemovalTest  getSET (com.example.castapp.ui.WindowRemovalTest  getSet (com.example.castapp.ui.WindowRemovalTest  invoke (com.example.castapp.ui.WindowRemovalTest  isBlank (com.example.castapp.ui.WindowRemovalTest  
isNotBlank (com.example.castapp.ui.WindowRemovalTest  mutableMapOf (com.example.castapp.ui.WindowRemovalTest  mutableSetOf (com.example.castapp.ui.WindowRemovalTest  repeat (com.example.castapp.ui.WindowRemovalTest  set (com.example.castapp.ui.WindowRemovalTest  RemoteTextWindowManager %com.example.castapp.ui.windowsettings  equals =com.example.castapp.ui.windowsettings.RemoteTextWindowManager  
hideEditPanel =com.example.castapp.ui.windowsettings.RemoteTextWindowManager  
showEditPanel =com.example.castapp.ui.windowsettings.RemoteTextWindowManager  
Connection com.example.castapp.utils  NotificationManager com.example.castapp.utils  NotificationManagerTest com.example.castapp.utils  assertEquals com.example.castapp.utils  
assertTrue com.example.castapp.utils  forEach com.example.castapp.utils  
isNotBlank com.example.castapp.utils  map com.example.castapp.utils  toSet com.example.castapp.utils  NotificationConfig -com.example.castapp.utils.NotificationManager  NotificationType -com.example.castapp.utils.NotificationManager  actions @com.example.castapp.utils.NotificationManager.NotificationConfig  content @com.example.castapp.utils.NotificationManager.NotificationConfig  	isOngoing @com.example.castapp.utils.NotificationManager.NotificationConfig  title @com.example.castapp.utils.NotificationManager.NotificationConfig  AUDIO_STREAMING >com.example.castapp.utils.NotificationManager.NotificationType  CASTING >com.example.castapp.utils.NotificationManager.NotificationType  FLOATING_STOPWATCH >com.example.castapp.utils.NotificationManager.NotificationType  	RECEIVING >com.example.castapp.utils.NotificationManager.NotificationType  channelDescription >com.example.castapp.utils.NotificationManager.NotificationType  	channelId >com.example.castapp.utils.NotificationManager.NotificationType  channelName >com.example.castapp.utils.NotificationManager.NotificationType  notificationId >com.example.castapp.utils.NotificationManager.NotificationType  values >com.example.castapp.utils.NotificationManager.NotificationType  
Connection 1com.example.castapp.utils.NotificationManagerTest  NotificationManager 1com.example.castapp.utils.NotificationManagerTest  Test 1com.example.castapp.utils.NotificationManagerTest  assertEquals 1com.example.castapp.utils.NotificationManagerTest  
assertTrue 1com.example.castapp.utils.NotificationManagerTest  forEach 1com.example.castapp.utils.NotificationManagerTest  getASSERTEquals 1com.example.castapp.utils.NotificationManagerTest  
getASSERTTrue 1com.example.castapp.utils.NotificationManagerTest  getAssertEquals 1com.example.castapp.utils.NotificationManagerTest  
getAssertTrue 1com.example.castapp.utils.NotificationManagerTest  
getFOREach 1com.example.castapp.utils.NotificationManagerTest  
getForEach 1com.example.castapp.utils.NotificationManagerTest  
getISNotBlank 1com.example.castapp.utils.NotificationManagerTest  
getIsNotBlank 1com.example.castapp.utils.NotificationManagerTest  getMAP 1com.example.castapp.utils.NotificationManagerTest  getMap 1com.example.castapp.utils.NotificationManagerTest  getTOSet 1com.example.castapp.utils.NotificationManagerTest  getToSet 1com.example.castapp.utils.NotificationManagerTest  
isNotBlank 1com.example.castapp.utils.NotificationManagerTest  map 1com.example.castapp.utils.NotificationManagerTest  toSet 1com.example.castapp.utils.NotificationManagerTest  ControlMessage com.example.castapp.websocket  TYPE_DISCONNECT ,com.example.castapp.websocket.ControlMessage  TYPE_FUNCTION_CONTROL ,com.example.castapp.websocket.ControlMessage  $TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL ,com.example.castapp.websocket.ControlMessage  TYPE_TEXT_FORMAT_SYNC ,com.example.castapp.websocket.ControlMessage  connectionId ,com.example.castapp.websocket.ControlMessage  createBatchWindowSyncControl ,com.example.castapp.websocket.ControlMessage  createDisconnect ,com.example.castapp.websocket.ControlMessage  createFunctionControl ,com.example.castapp.websocket.ControlMessage  createTextFormatSyncMessage ,com.example.castapp.websocket.ControlMessage  data ,com.example.castapp.websocket.ControlMessage  type ,com.example.castapp.websocket.ControlMessage  TYPE_DISCONNECT 6com.example.castapp.websocket.ControlMessage.Companion  TYPE_FUNCTION_CONTROL 6com.example.castapp.websocket.ControlMessage.Companion  $TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL 6com.example.castapp.websocket.ControlMessage.Companion  TYPE_TEXT_FORMAT_SYNC 6com.example.castapp.websocket.ControlMessage.Companion  createBatchWindowSyncControl 6com.example.castapp.websocket.ControlMessage.Companion  createDisconnect 6com.example.castapp.websocket.ControlMessage.Companion  createFunctionControl 6com.example.castapp.websocket.ControlMessage.Companion  createTextFormatSyncMessage 6com.example.castapp.websocket.ControlMessage.Companion  CastWindowInfo 	java.lang  
Connection 	java.lang  ControlMessage 	java.lang  	Exception 	java.lang  Float 	java.lang  Math 	java.lang  MockLayoutItem 	java.lang  NotificationManager 	java.lang  Pair 	java.lang  RemoteReceiverConnection 	java.lang  RemoteTextWindowManager 	java.lang  RobolectricTestRunner 	java.lang  RuntimeEnvironment 	java.lang  Triple 	java.lang  android 	java.lang  arrayOf 	java.lang  assert 	java.lang  assertEquals 	java.lang  assertFalse 	java.lang  assertNotEquals 	java.lang  
assertTrue 	java.lang  coerceIn 	java.lang  com 	java.lang  contains 	java.lang  contentToString 	java.lang  	emptyList 	java.lang  forEach 	java.lang  isBlank 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  listOf 	java.lang  map 	java.lang  mapOf 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  mutableSetOf 	java.lang  println 	java.lang  repeat 	java.lang  
roundToInt 	java.lang  set 	java.lang  
startsWith 	java.lang  take 	java.lang  to 	java.lang  toSet 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  abs java.lang.Math  max java.lang.Math  Any kotlin  Array kotlin  Boolean kotlin  CastWindowInfo kotlin  
Connection kotlin  ControlMessage kotlin  Double kotlin  	Exception kotlin  Float kotlin  	Function1 kotlin  Int kotlin  Long kotlin  Math kotlin  MockLayoutItem kotlin  Nothing kotlin  NotificationManager kotlin  Pair kotlin  RemoteReceiverConnection kotlin  RemoteTextWindowManager kotlin  RobolectricTestRunner kotlin  RuntimeEnvironment kotlin  String kotlin  Suppress kotlin  Triple kotlin  android kotlin  arrayOf kotlin  assert kotlin  assertEquals kotlin  assertFalse kotlin  assertNotEquals kotlin  
assertTrue kotlin  coerceIn kotlin  com kotlin  contains kotlin  contentToString kotlin  	emptyList kotlin  forEach kotlin  isBlank kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  listOf kotlin  map kotlin  mapOf kotlin  
mutableListOf kotlin  mutableMapOf kotlin  mutableSetOf kotlin  println kotlin  repeat kotlin  
roundToInt kotlin  set kotlin  
startsWith kotlin  take kotlin  to kotlin  toSet kotlin  getCONTENTToString kotlin.Array  getContentToString kotlin.Array  
getFOREach kotlin.Array  
getForEach kotlin.Array  getMAP kotlin.Array  getMap kotlin.Array  
getROUNDToInt 
kotlin.Double  
getRoundToInt 
kotlin.Double  getCOERCEIn kotlin.Float  getCoerceIn kotlin.Float  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  getTO kotlin.Pair  getTo kotlin.Pair  second kotlin.Pair  to kotlin.Pair  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISBlank 
kotlin.String  
getISNotBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  
getSTARTSWith 
kotlin.String  
getStartsWith 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  CastWindowInfo kotlin.annotation  
Connection kotlin.annotation  ControlMessage kotlin.annotation  	Exception kotlin.annotation  Float kotlin.annotation  Math kotlin.annotation  MockLayoutItem kotlin.annotation  NotificationManager kotlin.annotation  Pair kotlin.annotation  RemoteReceiverConnection kotlin.annotation  RemoteTextWindowManager kotlin.annotation  RobolectricTestRunner kotlin.annotation  RuntimeEnvironment kotlin.annotation  Triple kotlin.annotation  android kotlin.annotation  arrayOf kotlin.annotation  assert kotlin.annotation  assertEquals kotlin.annotation  assertFalse kotlin.annotation  assertNotEquals kotlin.annotation  
assertTrue kotlin.annotation  coerceIn kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  contentToString kotlin.annotation  	emptyList kotlin.annotation  forEach kotlin.annotation  isBlank kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  listOf kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  mutableSetOf kotlin.annotation  println kotlin.annotation  repeat kotlin.annotation  
roundToInt kotlin.annotation  set kotlin.annotation  
startsWith kotlin.annotation  take kotlin.annotation  to kotlin.annotation  toSet kotlin.annotation  CastWindowInfo kotlin.collections  
Connection kotlin.collections  ControlMessage kotlin.collections  	Exception kotlin.collections  Float kotlin.collections  List kotlin.collections  Map kotlin.collections  Math kotlin.collections  MockLayoutItem kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  NotificationManager kotlin.collections  Pair kotlin.collections  RemoteReceiverConnection kotlin.collections  RemoteTextWindowManager kotlin.collections  RobolectricTestRunner kotlin.collections  RuntimeEnvironment kotlin.collections  Set kotlin.collections  Triple kotlin.collections  android kotlin.collections  arrayOf kotlin.collections  assert kotlin.collections  assertEquals kotlin.collections  assertFalse kotlin.collections  assertNotEquals kotlin.collections  
assertTrue kotlin.collections  coerceIn kotlin.collections  com kotlin.collections  contains kotlin.collections  contentToString kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  isBlank kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  println kotlin.collections  repeat kotlin.collections  
roundToInt kotlin.collections  set kotlin.collections  
startsWith kotlin.collections  take kotlin.collections  to kotlin.collections  toSet kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getTOSet kotlin.collections.List  getToSet kotlin.collections.List  getTAKE kotlin.collections.MutableList  getTake kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  CastWindowInfo kotlin.comparisons  
Connection kotlin.comparisons  ControlMessage kotlin.comparisons  	Exception kotlin.comparisons  Float kotlin.comparisons  Math kotlin.comparisons  MockLayoutItem kotlin.comparisons  NotificationManager kotlin.comparisons  Pair kotlin.comparisons  RemoteReceiverConnection kotlin.comparisons  RemoteTextWindowManager kotlin.comparisons  RobolectricTestRunner kotlin.comparisons  RuntimeEnvironment kotlin.comparisons  Triple kotlin.comparisons  android kotlin.comparisons  arrayOf kotlin.comparisons  assert kotlin.comparisons  assertEquals kotlin.comparisons  assertFalse kotlin.comparisons  assertNotEquals kotlin.comparisons  
assertTrue kotlin.comparisons  coerceIn kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  contentToString kotlin.comparisons  	emptyList kotlin.comparisons  forEach kotlin.comparisons  isBlank kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  listOf kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  mutableSetOf kotlin.comparisons  println kotlin.comparisons  repeat kotlin.comparisons  
roundToInt kotlin.comparisons  set kotlin.comparisons  
startsWith kotlin.comparisons  take kotlin.comparisons  to kotlin.comparisons  toSet kotlin.comparisons  CastWindowInfo 	kotlin.io  
Connection 	kotlin.io  ControlMessage 	kotlin.io  	Exception 	kotlin.io  Float 	kotlin.io  Math 	kotlin.io  MockLayoutItem 	kotlin.io  NotificationManager 	kotlin.io  Pair 	kotlin.io  RemoteReceiverConnection 	kotlin.io  RemoteTextWindowManager 	kotlin.io  RobolectricTestRunner 	kotlin.io  RuntimeEnvironment 	kotlin.io  Triple 	kotlin.io  android 	kotlin.io  arrayOf 	kotlin.io  assert 	kotlin.io  assertEquals 	kotlin.io  assertFalse 	kotlin.io  assertNotEquals 	kotlin.io  
assertTrue 	kotlin.io  coerceIn 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  contentToString 	kotlin.io  	emptyList 	kotlin.io  forEach 	kotlin.io  isBlank 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  listOf 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  mutableSetOf 	kotlin.io  println 	kotlin.io  repeat 	kotlin.io  
roundToInt 	kotlin.io  set 	kotlin.io  
startsWith 	kotlin.io  take 	kotlin.io  to 	kotlin.io  toSet 	kotlin.io  CastWindowInfo 
kotlin.jvm  
Connection 
kotlin.jvm  ControlMessage 
kotlin.jvm  	Exception 
kotlin.jvm  Float 
kotlin.jvm  Math 
kotlin.jvm  MockLayoutItem 
kotlin.jvm  NotificationManager 
kotlin.jvm  Pair 
kotlin.jvm  RemoteReceiverConnection 
kotlin.jvm  RemoteTextWindowManager 
kotlin.jvm  RobolectricTestRunner 
kotlin.jvm  RuntimeEnvironment 
kotlin.jvm  Triple 
kotlin.jvm  android 
kotlin.jvm  arrayOf 
kotlin.jvm  assert 
kotlin.jvm  assertEquals 
kotlin.jvm  assertFalse 
kotlin.jvm  assertNotEquals 
kotlin.jvm  
assertTrue 
kotlin.jvm  coerceIn 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  contentToString 
kotlin.jvm  	emptyList 
kotlin.jvm  forEach 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  listOf 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  mutableSetOf 
kotlin.jvm  println 
kotlin.jvm  repeat 
kotlin.jvm  
roundToInt 
kotlin.jvm  set 
kotlin.jvm  
startsWith 
kotlin.jvm  take 
kotlin.jvm  to 
kotlin.jvm  toSet 
kotlin.jvm  
roundToInt kotlin.math  CastWindowInfo 
kotlin.ranges  
Connection 
kotlin.ranges  ControlMessage 
kotlin.ranges  	Exception 
kotlin.ranges  Float 
kotlin.ranges  IntRange 
kotlin.ranges  Math 
kotlin.ranges  MockLayoutItem 
kotlin.ranges  NotificationManager 
kotlin.ranges  Pair 
kotlin.ranges  RemoteReceiverConnection 
kotlin.ranges  RemoteTextWindowManager 
kotlin.ranges  RobolectricTestRunner 
kotlin.ranges  RuntimeEnvironment 
kotlin.ranges  Triple 
kotlin.ranges  android 
kotlin.ranges  arrayOf 
kotlin.ranges  assert 
kotlin.ranges  assertEquals 
kotlin.ranges  assertFalse 
kotlin.ranges  assertNotEquals 
kotlin.ranges  
assertTrue 
kotlin.ranges  coerceIn 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  contentToString 
kotlin.ranges  	emptyList 
kotlin.ranges  forEach 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  listOf 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  mutableSetOf 
kotlin.ranges  println 
kotlin.ranges  repeat 
kotlin.ranges  
roundToInt 
kotlin.ranges  set 
kotlin.ranges  
startsWith 
kotlin.ranges  take 
kotlin.ranges  to 
kotlin.ranges  toSet 
kotlin.ranges  contains kotlin.ranges.IntProgression  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  CastWindowInfo kotlin.sequences  
Connection kotlin.sequences  ControlMessage kotlin.sequences  	Exception kotlin.sequences  Float kotlin.sequences  Math kotlin.sequences  MockLayoutItem kotlin.sequences  NotificationManager kotlin.sequences  Pair kotlin.sequences  RemoteReceiverConnection kotlin.sequences  RemoteTextWindowManager kotlin.sequences  RobolectricTestRunner kotlin.sequences  RuntimeEnvironment kotlin.sequences  Triple kotlin.sequences  android kotlin.sequences  arrayOf kotlin.sequences  assert kotlin.sequences  assertEquals kotlin.sequences  assertFalse kotlin.sequences  assertNotEquals kotlin.sequences  
assertTrue kotlin.sequences  coerceIn kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  contentToString kotlin.sequences  	emptyList kotlin.sequences  forEach kotlin.sequences  isBlank kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  listOf kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  mutableSetOf kotlin.sequences  println kotlin.sequences  repeat kotlin.sequences  
roundToInt kotlin.sequences  set kotlin.sequences  
startsWith kotlin.sequences  take kotlin.sequences  to kotlin.sequences  toSet kotlin.sequences  CastWindowInfo kotlin.text  
Connection kotlin.text  ControlMessage kotlin.text  	Exception kotlin.text  Float kotlin.text  Math kotlin.text  MockLayoutItem kotlin.text  NotificationManager kotlin.text  Pair kotlin.text  RemoteReceiverConnection kotlin.text  RemoteTextWindowManager kotlin.text  RobolectricTestRunner kotlin.text  RuntimeEnvironment kotlin.text  Triple kotlin.text  android kotlin.text  arrayOf kotlin.text  assert kotlin.text  assertEquals kotlin.text  assertFalse kotlin.text  assertNotEquals kotlin.text  
assertTrue kotlin.text  coerceIn kotlin.text  com kotlin.text  contains kotlin.text  contentToString kotlin.text  	emptyList kotlin.text  forEach kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  listOf kotlin.text  map kotlin.text  mapOf kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  mutableSetOf kotlin.text  println kotlin.text  repeat kotlin.text  
roundToInt kotlin.text  set kotlin.text  
startsWith kotlin.text  take kotlin.text  to kotlin.text  toSet kotlin.text  Assert 	org.junit  Before 	org.junit  Test 	org.junit  CastWindowInfo org.junit.Assert  
Connection org.junit.Assert  ControlMessage org.junit.Assert  	Exception org.junit.Assert  Float org.junit.Assert  Math org.junit.Assert  MockLayoutItem org.junit.Assert  NotificationManager org.junit.Assert  Pair org.junit.Assert  Triple org.junit.Assert  arrayOf org.junit.Assert  assertEquals org.junit.Assert  assertFalse org.junit.Assert  assertNotEquals org.junit.Assert  
assertTrue org.junit.Assert  coerceIn org.junit.Assert  contains org.junit.Assert  contentToString org.junit.Assert  	emptyList org.junit.Assert  forEach org.junit.Assert  isBlank org.junit.Assert  
isNotBlank org.junit.Assert  listOf org.junit.Assert  map org.junit.Assert  mapOf org.junit.Assert  
mutableListOf org.junit.Assert  mutableMapOf org.junit.Assert  mutableSetOf org.junit.Assert  println org.junit.Assert  repeat org.junit.Assert  
roundToInt org.junit.Assert  set org.junit.Assert  
startsWith org.junit.Assert  take org.junit.Assert  to org.junit.Assert  toSet org.junit.Assert  RunWith org.junit.runner  RobolectricTestRunner org.robolectric  RuntimeEnvironment org.robolectric  getApplication "org.robolectric.RuntimeEnvironment                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               