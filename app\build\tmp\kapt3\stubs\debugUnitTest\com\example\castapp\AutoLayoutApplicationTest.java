package com.example.castapp;

/**
 * 自动布局应用功能测试
 * 验证发送端投屏时自动应用布局参数的功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001:\u0001\nB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0002J\b\u0010\u0005\u001a\u00020\u0004H\u0002J\b\u0010\u0006\u001a\u00020\u0007H\u0007J\b\u0010\b\u001a\u00020\u0007H\u0007J\b\u0010\t\u001a\u00020\u0007H\u0007\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/AutoLayoutApplicationTest;", "", "()V", "createMockLayoutItem", "Lcom/example/castapp/AutoLayoutApplicationTest$MockLayoutItem;", "createMockLayoutItemWithCrop", "testAntiFlickerMechanism", "", "testAutoLayoutApplicationFlow", "testLayoutParameterMapping", "MockLayoutItem", "app_debugUnitTest"})
public final class AutoLayoutApplicationTest {
    
    public AutoLayoutApplicationTest() {
        super();
    }
    
    @org.junit.Test()
    public final void testAutoLayoutApplicationFlow() {
    }
    
    @org.junit.Test()
    public final void testLayoutParameterMapping() {
    }
    
    @org.junit.Test()
    public final void testAntiFlickerMechanism() {
    }
    
    private final com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem createMockLayoutItem() {
        return null;
    }
    
    private final com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem createMockLayoutItemWithCrop() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b$\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bo\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\f\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\n\u0012\u0006\u0010\u000e\u001a\u00020\u0005\u0012\u0006\u0010\u000f\u001a\u00020\u0005\u0012\u0006\u0010\u0010\u001a\u00020\n\u0012\b\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0012J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\nH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0005H\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\'\u001a\u00020\nH\u00c6\u0003J\t\u0010(\u001a\u00020\nH\u00c6\u0003J\t\u0010)\u001a\u00020\nH\u00c6\u0003J\t\u0010*\u001a\u00020\nH\u00c6\u0003J\u008d\u0001\u0010+\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\n2\b\b\u0002\u0010\u000e\u001a\u00020\u00052\b\b\u0002\u0010\u000f\u001a\u00020\u00052\b\b\u0002\u0010\u0010\u001a\u00020\n2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010,\u001a\u00020\n2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\t\u00100\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u000f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0019R\u0011\u0010\r\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0019R\u0011\u0010\f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0019R\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0019R\u0011\u0010\u0010\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0019R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0014R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0014R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0014\u00a8\u00061"}, d2 = {"Lcom/example/castapp/AutoLayoutApplicationTest$MockLayoutItem;", "", "deviceId", "", "positionX", "", "positionY", "scaleFactor", "rotationAngle", "isDragEnabled", "", "isScaleEnabled", "isRotationEnabled", "isMirrored", "cornerRadius", "alpha", "isVisible", "cropRect", "(Ljava/lang/String;FFFFZZZZFFZLjava/lang/String;)V", "getAlpha", "()F", "getCornerRadius", "getCropRect", "()Ljava/lang/String;", "getDeviceId", "()Z", "getPositionX", "getPositionY", "getRotationAngle", "getScaleFactor", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debugUnitTest"})
    public static final class MockLayoutItem {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String deviceId = null;
        private final float positionX = 0.0F;
        private final float positionY = 0.0F;
        private final float scaleFactor = 0.0F;
        private final float rotationAngle = 0.0F;
        private final boolean isDragEnabled = false;
        private final boolean isScaleEnabled = false;
        private final boolean isRotationEnabled = false;
        private final boolean isMirrored = false;
        private final float cornerRadius = 0.0F;
        private final float alpha = 0.0F;
        private final boolean isVisible = false;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String cropRect = null;
        
        public MockLayoutItem(@org.jetbrains.annotations.NotNull()
        java.lang.String deviceId, float positionX, float positionY, float scaleFactor, float rotationAngle, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isMirrored, float cornerRadius, float alpha, boolean isVisible, @org.jetbrains.annotations.Nullable()
        java.lang.String cropRect) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDeviceId() {
            return null;
        }
        
        public final float getPositionX() {
            return 0.0F;
        }
        
        public final float getPositionY() {
            return 0.0F;
        }
        
        public final float getScaleFactor() {
            return 0.0F;
        }
        
        public final float getRotationAngle() {
            return 0.0F;
        }
        
        public final boolean isDragEnabled() {
            return false;
        }
        
        public final boolean isScaleEnabled() {
            return false;
        }
        
        public final boolean isRotationEnabled() {
            return false;
        }
        
        public final boolean isMirrored() {
            return false;
        }
        
        public final float getCornerRadius() {
            return 0.0F;
        }
        
        public final float getAlpha() {
            return 0.0F;
        }
        
        public final boolean isVisible() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getCropRect() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final float component10() {
            return 0.0F;
        }
        
        public final float component11() {
            return 0.0F;
        }
        
        public final boolean component12() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component13() {
            return null;
        }
        
        public final float component2() {
            return 0.0F;
        }
        
        public final float component3() {
            return 0.0F;
        }
        
        public final float component4() {
            return 0.0F;
        }
        
        public final float component5() {
            return 0.0F;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final boolean component7() {
            return false;
        }
        
        public final boolean component8() {
            return false;
        }
        
        public final boolean component9() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.AutoLayoutApplicationTest.MockLayoutItem copy(@org.jetbrains.annotations.NotNull()
        java.lang.String deviceId, float positionX, float positionY, float scaleFactor, float rotationAngle, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isMirrored, float cornerRadius, float alpha, boolean isVisible, @org.jetbrains.annotations.Nullable()
        java.lang.String cropRect) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}