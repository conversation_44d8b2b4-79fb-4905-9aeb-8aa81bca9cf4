Bapp/src/test/java/com/example/castapp/AutoLayoutApplicationTest.kt<app/src/test/java/com/example/castapp/BatchWindowSyncTest.ktDapp/src/test/java/com/example/castapp/CornerRadiusConsistencyTest.kt<app/src/test/java/com/example/castapp/CropPositionFixTest.kt8app/src/test/java/com/example/castapp/ExampleUnitTest.kt=app/src/test/java/com/example/castapp/PrecisionControlTest.ktIapp/src/test/java/com/example/castapp/RemoteTextEditingIntegrationTest.ktDapp/src/test/java/com/example/castapp/RemoteTextWindowManagerTest.kt>app/src/test/java/com/example/castapp/ResolutionManagerTest.ktCapp/src/test/java/com/example/castapp/TextWindowRotationSyncTest.kt@app/src/test/java/com/example/castapp/TextWindowSizeSync_Test.ktKapp/src/test/java/com/example/castapp/integration/ScalingIntegrationTest.ktJapp/src/test/java/com/example/castapp/service/AudioStreamingServiceTest.ktGapp/src/test/java/com/example/castapp/ui/DisconnectionSimulationTest.kt=app/src/test/java/com/example/castapp/ui/WindowRemovalTest.ktBapp/src/test/java/com/example/castapp/ui/view/WindowScalingTest.ktFapp/src/test/java/com/example/castapp/utils/NotificationManagerTest.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             