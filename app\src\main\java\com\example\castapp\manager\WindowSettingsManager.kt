package com.example.castapp.manager

import android.app.Activity
import android.graphics.RectF
import android.widget.FrameLayout
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.manager.windowsettings.*
import com.example.castapp.utils.AppLog
import com.example.castapp.service.ReceivingService

/**
 * 窗口管理器（重构版）
 * 采用模块化设计，将功能委托给各个专门的模块处理
 * 保持原有的公共API不变，确保向后兼容
 */
class WindowSettingsManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: WindowSettingsManager? = null

        fun getInstance(): WindowSettingsManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WindowSettingsManager().also { INSTANCE = it }
            }
        }
    }

    // 功能模块
    private lateinit var dataModule: WindowDataModule
    private lateinit var creationModule: WindowCreationModule
    private lateinit var lifecycleModule: WindowLifecycleModule
    private lateinit var operationModule: WindowOperationModule
    private lateinit var layoutModule: WindowLayoutModule
    private lateinit var infoModule: WindowInfoModule
    private lateinit var dialogModule: WindowDialogModule

    // 回调
    private var precisionControlUpdateCallback: (() -> Unit)? = null
    private var transformValueChangeCallback: ((String, Float, Float, Float, Float) -> Unit)? = null

    // 初始化标志
    private var isInitialized = false

    /**
     * 初始化管理器
     */
    fun initialize(activity: Activity, container: FrameLayout) {
        if (isInitialized) {
            AppLog.d("CastWindowManager已经初始化，跳过重复初始化")
            return
        }

        // 初始化数据模块
        dataModule = WindowDataModule()
        dataModule.initialize(activity, container)

        // 初始化创建模块
        creationModule = WindowCreationModule(dataModule)

        // 初始化生命周期模块
        lifecycleModule = WindowLifecycleModule(dataModule, creationModule)

        // 初始化操作模块
        operationModule = WindowOperationModule(dataModule)

        // 初始化布局模块
        layoutModule = WindowLayoutModule(dataModule)

        // 初始化信息模块
        infoModule = WindowInfoModule(dataModule)

        // 初始化对话框模块
        dialogModule = WindowDialogModule(dataModule, infoModule)

        // 设置模块间的回调关系
        setupModuleCallbacks()

        isInitialized = true
        AppLog.d("CastWindowManager模块化初始化完成")
    }

    /**
     * 设置模块间的回调关系
     */
    private fun setupModuleCallbacks() {
        // 生命周期模块回调
        lifecycleModule.setPrecisionControlUpdateCallback {
            precisionControlUpdateCallback?.invoke()
        }
        lifecycleModule.setDialogRefreshCallback {
            dialogModule.refreshWindowManagerDialogIfVisible()
        }

        // 操作模块回调
        operationModule.setPrecisionControlUpdateCallback {
            precisionControlUpdateCallback?.invoke()
        }
        operationModule.setDialogRefreshCallback {
            dialogModule.refreshWindowManagerDialogIfVisible()
        }

        // 布局模块回调
        layoutModule.setDialogRefreshCallback {
            dialogModule.refreshWindowManagerDialogIfVisible()
        }

        // 创建模块回调
        creationModule.setTransformValueChangeCallback { connId, x, y, scale, rotation ->
            transformValueChangeCallback?.invoke(connId, x, y, scale, rotation)
        }

        // 对话框模块回调
        dialogModule.setWindowLayerAdjustCallback { newOrderList ->
            layoutModule.adjustWindowLayers(newOrderList)
        }
        dialogModule.setWindowOperationCallbacks(
            onCropSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleCropMode(connectionId, isEnabled)
            },
            onDragSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleDragMode(connectionId, isEnabled)
            },
            onScaleSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleScaleMode(connectionId, isEnabled)
            },
            onRotationSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleRotationMode(connectionId, isEnabled)
            },
            onVisibilitySwitchChanged = { connectionId, isVisible ->
                operationModule.toggleWindowVisibility(connectionId, isVisible)
            },
            onMirrorSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleMirrorMode(connectionId, isEnabled)
            },
            onCornerRadiusChanged = { connectionId, cornerRadius ->
                operationModule.setWindowCornerRadius(connectionId, cornerRadius)
            },
            onAlphaChanged = { connectionId, alpha ->
                operationModule.setWindowAlpha(connectionId, alpha)
            },
            onControlSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleControlMode(connectionId, isEnabled)
            },
            onEditSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleEditMode(connectionId, isEnabled)
            },

            onBorderSwitchChanged = { connectionId, isEnabled ->
                operationModule.toggleBorderMode(connectionId, isEnabled)
            },
            onBorderColorChanged = { connectionId, color ->
                operationModule.setBorderColor(connectionId, color)
            },
            onBorderWidthChanged = { connectionId, width ->
                operationModule.setBorderWidth(connectionId, width)
            },
            onNoteChanged = { connectionId, note ->
                // 🏷️ 备注变更处理：只需要保存到SharedPreferences，不需要操作窗口
                val activity = dataModule.getCurrentActivity()
                if (activity != null) {
                    val noteManager = com.example.castapp.utils.NoteManager(activity)
                    noteManager.saveNote(connectionId, note)
                    AppLog.d("【备注】备注已保存: $connectionId -> $note")

                    // 刷新对话框以更新显示
                    dialogModule.refreshWindowManagerDialogIfVisible()
                } else {
                    AppLog.w("【备注】无法获取Activity上下文，备注保存失败")
                }
            },
            onLandscapeSwitchChanged = { connectionId, isEnabled ->
                handleLandscapeModeSwitch(connectionId, isEnabled)
            }
        )

        // 🗑️ 设置窗口删除回调
        dialogModule.setWindowDeleteCallback { connectionId ->
            handleWindowDelete(connectionId)
        }

        // 🎬 设置视频控制回调
        dialogModule.setVideoControlCallbacks(
            onVideoPlaySwitchChanged = { connectionId, isEnabled ->
                handleVideoPlaySwitch(connectionId, isEnabled)
            },
            onVideoLoopCountChanged = { connectionId, loopCount ->
                handleVideoLoopCount(connectionId, loopCount)
            },
            onVideoVolumeChanged = { connectionId, volume ->
                handleVideoVolume(connectionId, volume)
            }
        )
    }

    // ==================== 公共API方法（保持向后兼容） ====================

    /**
     * 设置精准控制面板更新回调
     */
    fun setPrecisionControlUpdateCallback(callback: () -> Unit) {
        this.precisionControlUpdateCallback = callback
    }

    /**
     * 设置变换值变化回�?     */
    fun setTransformValueChangeCallback(callback: (String, Float, Float, Float, Float) -> Unit) {
        this.transformValueChangeCallback = callback
    }

    /**
     * 处理新连接事�?     */
    fun handleNewConnection(connectionId: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法处理新连接事件")
            return
        }
        lifecycleModule.handleNewConnection(connectionId)
    }

    /**
     * 处理屏幕分辨率事件
     */
    fun handleScreenResolution(connectionId: String, width: Int, height: Int) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法处理分辨率事件")
            return
        }
        lifecycleModule.handleScreenResolution(connectionId, width, height)
    }

    /**
     * 🎯 横竖屏适配：处理视频方向变化
     */
    fun handleVideoOrientationChanged(connectionId: String, orientation: Int, videoWidth: Int = 0, videoHeight: Int = 0) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法处理视频方向变化")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setVideoOrientation(orientation, videoWidth, videoHeight)
                    AppLog.d("🎯 已设置视频方向: $connectionId, 方向: $orientation, 分辨率: ${videoWidth}×${videoHeight}")
                } else {
                    AppLog.w("🎯 未找到连接的投屏窗口，无法设置视频方向: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("🎯 设置视频方向失败: $connectionId", e)
            }
        }
    }

    /**
     * 移除指定连接的投屏窗口
     */
    fun removeTextureViewForConnection(connectionId: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法移除窗口")
            return
        }
        lifecycleModule.removeWindowForConnection(connectionId)
    }

    /**
     * 移除所有投屏窗口
     */
    fun removeAllTextureViews() {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法移除所有窗口")
            return
        }
        lifecycleModule.removeAllWindows()
    }

    /**
     * 显示窗口管理BottomSheet
     */
    fun showWindowManagerDialog() {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法显示窗口管理对话框")
            return
        }
        dialogModule.showWindowManagerDialog()
    }

    /**
     * 显示层级管理BottomSheet
     */
    fun showLayerManagerDialog() {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法显示层级管理对话框")
            return
        }
        dialogModule.showLayerManagerDialog()
    }

    /**
     * 刷新窗口管理对话框
     */
    fun refreshWindowManagerDialog() {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法刷新窗口管理对话框")
            return
        }
        dialogModule.refreshWindowManagerDialog()
    }

    /**
     * 🔄 刷新层级管理对话框
     */
    fun refreshLayerManagerDialog() {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法刷新层级管理对话框")
            return
        }
        dialogModule.refreshLayerManagerDialog()
    }

    /**
     * 获取当前活跃的投屏窗口信息列表
     */
    fun getCurrentWindowInfoList(): List<CastWindowInfo> {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，返回空列表")
            return emptyList()
        }
        return infoModule.getCurrentWindowInfoList()
    }



    /**
     * 应用布局项到现有窗口（直接使用布局项列表）
     */
    fun applyLayoutItemsToExistingWindows(layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用布局项")
            return
        }
        layoutModule.applyLayoutItemsToExistingWindows(layoutItems)
    }

    // ==================== 精准控制相关方法 ====================

    /**
     * 应用精准变换
     */
    fun applyPrecisionTransform(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用精准变换")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.setPrecisionTransform(x, y, scale, rotation)
                    AppLog.d("【精准控制】应用变换: $connectionId, X=$x, Y=$y, Scale=$scale, Rotation=$rotation")
                } else {
                    AppLog.w("【精准控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【精准控制】应用变换失败: $connectionId", e)
            }
        }
    }

    /**
     * 🎯 应用远程位置控制（增强调试版）
     */
    fun applyRemotePosition(connectionId: String, x: Float, y: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程位置")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 🎯 详细调试：分析接收端窗口状态
                    val isCroppedWindow = transformHandler.isCroppingMode()
                    val cropRectRatio = transformHandler.getCropRectRatio()
                    val currentContainerX = transformHandler.getContainerDisplayX()
                    val currentContainerY = transformHandler.getContainerDisplayY()
                    val currentActualX = transformHandler.getActualDisplayX()
                    val currentActualY = transformHandler.getActualDisplayY()

                    AppLog.d("【远程位置控制调试】接收端窗口状态分析: $connectionId")
                    AppLog.d("  是否裁剪: $isCroppedWindow")
                    cropRectRatio?.let { cropRatio ->
                        AppLog.d("  裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
                    }
                    AppLog.d("  当前容器位置: ($currentContainerX, $currentContainerY)")
                    AppLog.d("  当前可见区域位置: ($currentActualX, $currentActualY)")
                    AppLog.d("  遥控端发送的坐标: ($x, $y)")
                    AppLog.d("  🎯 setPrecisionTransform期望的是可见区域位置")

                    // 获取当前缩放和旋转，只更新位置
                    val currentScale = transformHandler.getCurrentScaleFactor()
                    val currentRotation = transformHandler.getCurrentRotation()

                    // 应用新的位置，保持其他变换不变
                    transformHandler.setPrecisionTransform(x, y, currentScale, currentRotation)

                    // 🎯 验证应用后的位置
                    val newContainerX = transformHandler.getContainerDisplayX()
                    val newContainerY = transformHandler.getContainerDisplayY()
                    val newActualX = transformHandler.getActualDisplayX()
                    val newActualY = transformHandler.getActualDisplayY()

                    AppLog.d("【远程位置控制调试】应用后的位置验证:")
                    AppLog.d("  新容器位置: ($newContainerX, $newContainerY)")
                    AppLog.d("  新可见区域位置: ($newActualX, $newActualY)")
                    AppLog.d("  位置变化 - 容器: (${newContainerX - currentContainerX}, ${newContainerY - currentContainerY})")
                    AppLog.d("  位置变化 - 可见区域: (${newActualX - currentActualX}, ${newActualY - currentActualY})")

                } else {
                    AppLog.w("【远程位置控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【远程位置控制】应用位置失败: $connectionId", e)
            }
        }
    }

    /**
     * 🎯 应用远程缩放控制
     */
    fun applyRemoteScale(connectionId: String, scaleFactor: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程缩放")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 获取当前位置和旋转，只更新缩放
                    val currentX = transformHandler.getActualDisplayX()
                    val currentY = transformHandler.getActualDisplayY()
                    val currentRotation = transformHandler.getCurrentRotation()

                    // 应用新的缩放，保持其他变换不变
                    transformHandler.setPrecisionTransform(currentX, currentY, scaleFactor, currentRotation)
                    AppLog.d("【远程缩放控制】应用缩放: $connectionId, Scale=$scaleFactor")
                } else {
                    AppLog.w("【远程缩放控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【远程缩放控制】应用缩放失败: $connectionId", e)
            }
        }
    }

    /**
     * 🎯 应用远程旋转控制
     */
    fun applyRemoteRotation(connectionId: String, rotationAngle: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程旋转")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 获取当前位置和缩放，只更新旋转
                    val currentX = transformHandler.getActualDisplayX()
                    val currentY = transformHandler.getActualDisplayY()
                    val currentScale = transformHandler.getCurrentScaleFactor()

                    // 应用新的旋转，保持其他变换不变
                    transformHandler.setPrecisionTransform(currentX, currentY, currentScale, rotationAngle)
                    AppLog.d("【远程旋转控制】应用旋转: $connectionId, Rotation=$rotationAngle°")
                } else {
                    AppLog.w("【远程旋转控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【远程旋转控制】应用旋转失败: $connectionId", e)
            }
        }
    }

    /**
     * 🎯 应用远程裁剪控制
     */
    fun applyRemoteCrop(connectionId: String, cropRatio: RectF) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程裁剪")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 应用裁剪
                    transformHandler.setCropRectRatio(cropRatio)
                    AppLog.d("【远程裁剪控制】应用裁剪: $connectionId, CropRatio=$cropRatio")
                } else {
                    AppLog.w("【远程裁剪控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【远程裁剪控制】应用裁剪失败: $connectionId", e)
            }
        }
    }

    /**
     * 🎯 增强型同步：应用远程缩放和位置组合控制
     */
    fun applyRemoteScaleAndPosition(connectionId: String, scaleFactor: Float, x: Float, y: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程缩放和位置")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 获取当前旋转，应用新的缩放和位置
                    val currentRotation = transformHandler.getCurrentRotation()

                    // 🎯 关键：原子性应用缩放和位置，确保同步
                    transformHandler.setPrecisionTransform(x, y, scaleFactor, currentRotation)
                    AppLog.d("【远程缩放和位置控制】应用组合变换: $connectionId, Scale=$scaleFactor, Position=($x, $y)")
                } else {
                    AppLog.w("【远程缩放和位置控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【远程缩放和位置控制】应用组合变换失败: $connectionId", e)
            }
        }
    }

    /**
     * 🎯 增强型同步：应用远程旋转和位置组合控制
     */
    fun applyRemoteRotationAndPosition(connectionId: String, rotationAngle: Float, x: Float, y: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程旋转和位置")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    // 获取当前缩放，应用新的旋转和位置
                    val currentScale = transformHandler.getCurrentScaleFactor()

                    // 🎯 关键：原子性应用旋转和位置，确保同步
                    transformHandler.setPrecisionTransform(x, y, currentScale, rotationAngle)
                    AppLog.d("【远程旋转和位置控制】应用组合变换: $connectionId, Rotation=$rotationAngle°, Position=($x, $y)")
                } else {
                    AppLog.w("【远程旋转和位置控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【远程旋转和位置控制】应用组合变换失败: $connectionId", e)
            }
        }
    }

    /**
     * 重置窗口变换
     */
    fun resetWindowTransform(connectionId: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法重置变换")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return
        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    transformHandler.resetTransform()
                    AppLog.d("【精准控制】重置变换: $connectionId")
                } else {
                    AppLog.w("【精准控制】未找到连接的投屏窗口: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("【精准控制】重置变换失败: $connectionId", e)
            }
        }
    }

    /**
     * 切换精准调控功能
     */
    fun toggleControlMode(connectionId: String, isEnabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法切换精准调控功能")
            return
        }
        operationModule.toggleControlMode(connectionId, isEnabled)
    }

    // ==================== 🔄 远程窗口变换控制方法 ====================

    /**
     * 🔄 设置拖动功能启用状态
     */
    fun setDragEnabled(connectionId: String, enabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置拖动功能")
            return
        }
        operationModule.toggleDragMode(connectionId, enabled)
    }

    /**
     * 🔄 设置缩放功能启用状态
     */
    fun setScaleEnabled(connectionId: String, enabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置缩放功能")
            return
        }
        operationModule.toggleScaleMode(connectionId, enabled)
    }

    /**
     * 🔄 设置旋转功能启用状态
     */
    fun setRotationEnabled(connectionId: String, enabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置旋转功能")
            return
        }
        operationModule.toggleRotationMode(connectionId, enabled)
    }

    /**
     * 🔄 设置裁剪功能启用状态
     */
    fun setCropEnabled(connectionId: String, enabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置裁剪功能")
            return
        }
        operationModule.toggleCropMode(connectionId, enabled)
    }

    /**
     * 🔄 设置窗口可见性
     */
    fun setWindowVisibility(connectionId: String, visible: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置窗口可见性")
            return
        }
        operationModule.toggleWindowVisibility(connectionId, visible)
    }

    /**
     * 🔄 设置镜像功能启用状态
     */
    fun setMirrorEnabled(connectionId: String, enabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置镜像功能")
            return
        }
        operationModule.toggleMirrorMode(connectionId, enabled)
    }

    /**
     * 📝 设置编辑功能启用状态（仅文本窗口）
     */
    fun setEditState(connectionId: String, isEnabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置编辑状态")
            return
        }
        dataModule.setEditState(connectionId, isEnabled)
        AppLog.d("【窗口设置管理器】设置编辑状态: $connectionId -> $isEnabled")

        // 🔧 修复：不在这里触发对话框刷新，避免循环刷新
        // 对话框刷新由TextWindowManager在编辑面板显示完成后触发
    }

    /**
     * 📝 获取编辑功能启用状态（仅文本窗口）
     */
    fun getEditState(connectionId: String): Boolean {
        return if (isInitialized) {
            dataModule.getEditState(connectionId)
        } else {
            AppLog.w("CastWindowManager未初始化，无法获取编辑状态")
            false
        }
    }

    /**
     * 🔧 获取拖动开关状态（用户设置的状态）
     */
    fun getDragSwitchState(connectionId: String): Boolean {
        return if (isInitialized) {
            dataModule.getDragSwitchState(connectionId)
        } else {
            AppLog.w("CastWindowManager未初始化，无法获取拖动开关状态")
            false
        }
    }

    /**
     * 🔄 设置圆角半径
     */
    fun setCornerRadius(connectionId: String, radius: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置圆角半径")
            return
        }
        operationModule.setWindowCornerRadius(connectionId, radius)
    }

    /**
     * 🔄 设置窗口透明度
     */
    fun setWindowAlpha(connectionId: String, alpha: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置窗口透明度")
            return
        }
        operationModule.setWindowAlpha(connectionId, alpha)
    }

    /**
     * 🎯 应用远程层级调整
     * 用于处理来自遥控端的层级调整指令
     */
    fun applyRemoteLayerOrder(orderedWindowList: List<com.example.castapp.model.CastWindowInfo>) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法应用远程层级调整")
            return
        }

        AppLog.d("【远程层级调整】应用层级调整: ${orderedWindowList.size} 个窗口")
        layoutModule.adjustWindowLayers(orderedWindowList)
    }

    /**
     * 🎯 获取窗口信息
     * 用于远程控制服务器获取窗口信息
     */
    fun getWindowInfo(connectionId: String): com.example.castapp.model.CastWindowInfo? {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法获取窗口信息")
            return null
        }

        // 从活跃窗口列表中查找指定的窗口信息
        val activeWindowList = infoModule.getActiveWindowInfoList()
        return activeWindowList.find { it.connectionId == connectionId }
    }

    /**
     * 🔄 设置边框启用状态
     */
    fun setBorderEnabled(connectionId: String, enabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置边框功能")
            return
        }
        operationModule.toggleBorderMode(connectionId, enabled)
    }

    /**
     * 🔄 设置边框颜色
     */
    fun setBorderColor(connectionId: String, color: Int) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置边框颜色")
            return
        }
        operationModule.setBorderColor(connectionId, color)
    }

    /**
     * 🔄 设置边框宽度
     */
    fun setBorderWidth(connectionId: String, width: Float) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法设置边框宽度")
            return
        }
        operationModule.setBorderWidth(connectionId, width)
    }

    // ==================== 截图功能相关方法 ====================

    /**
     * 捕获所有投屏窗口的截图
     * @param callback 截图完成回调，参数为截图数据列表
     * @param forRemoteControl 🎯 是否为遥控端请求，true时返回原始截图，false时返回变换后截图
     */
    fun captureAllWindowScreenshots(
        callback: (List<Map<String, Any>>) -> Unit,
        forRemoteControl: Boolean = false
    ) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法截图")
            callback(emptyList())
            return
        }

        val activity = dataModule.getCurrentActivity() ?: run {
            AppLog.w("Activity未设置，无法截图")
            callback(emptyList())
            return
        }

        activity.runOnUiThread {
            try {
                val screenshotDataList = mutableListOf<Map<String, Any>>()
                val windowMappings = dataModule.getAllWindowMappings()

                AppLog.d("📸 开始捕获所有投屏窗口截图，窗口数量: ${windowMappings.size}")

                if (windowMappings.isEmpty()) {
                    AppLog.w("📸 没有活跃的投屏窗口")
                    callback(emptyList())
                    return@runOnUiThread
                }

                // 遍历所有窗口进行截图
                windowMappings.forEach { (connectionId, transformHandler) ->
                    try {
                        AppLog.d("📸 正在截图窗口: $connectionId, 遥控端请求: $forRemoteControl")

                        // 🎯 调用 TransformHandler 的截图方法，传递forRemoteControl参数
                        val screenshot = transformHandler.captureScreenshot(forRemoteControl)

                        if (screenshot != null) {
                            // 将 Bitmap 转换为 Base64 字符串
                            val base64String = bitmapToBase64(screenshot)

                            if (base64String != null) {
                                val screenshotData = mapOf(
                                    "connectionId" to connectionId,
                                    "imageData" to base64String,
                                    "width" to screenshot.width,
                                    "height" to screenshot.height,
                                    "timestamp" to System.currentTimeMillis()
                                )
                                screenshotDataList.add(screenshotData)
                                AppLog.d("📸 窗口截图成功: $connectionId, 尺寸: ${screenshot.width}x${screenshot.height}")
                            } else {
                                AppLog.w("📸 截图转换Base64失败: $connectionId")
                            }

                            // 释放 Bitmap 资源
                            screenshot.recycle()
                        } else {
                            AppLog.w("📸 窗口截图失败: $connectionId")
                        }

                    } catch (e: Exception) {
                        AppLog.e("📸 截图窗口时发生异常: $connectionId", e)
                    }
                }

                AppLog.d("📸 所有窗口截图完成，成功截图数量: ${screenshotDataList.size}")
                callback(screenshotDataList)

            } catch (e: Exception) {
                AppLog.e("📸 捕获所有窗口截图失败", e)
                callback(emptyList())
            }
        }
    }

    /**
     * 将 Bitmap 转换为 Base64 字符串
     */
    private fun bitmapToBase64(bitmap: android.graphics.Bitmap): String? {
        return try {
            val byteArrayOutputStream = java.io.ByteArrayOutputStream()
            bitmap.compress(android.graphics.Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
            val byteArray = byteArrayOutputStream.toByteArray()
            android.util.Base64.encodeToString(byteArray, android.util.Base64.DEFAULT)
        } catch (e: Exception) {
            AppLog.e("📸 Bitmap转Base64失败", e)
            null
        }
    }

    // ==================== 摄像头窗口管理 ====================

    /**
     * 创建摄像头窗口
     */
    fun createCameraWindow(cameraId: String, cameraName: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法创建摄像头窗口")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                // 检查是否已存在相同ID的摄像头窗口
                if (dataModule.containsWindow(cameraId)) {
                    AppLog.d("摄像头窗口已存在: $cameraName (ID: $cameraId)")
                    return@runOnUiThread
                }

                // 创建摄像头窗口
                creationModule.createCameraWindow(cameraId, cameraName)

                AppLog.d("摄像头窗口创建完成: $cameraName (ID: $cameraId)")

            } catch (e: Exception) {
                AppLog.e("创建摄像头窗口失败: $cameraName", e)
            }
        }
    }

    // ==================== 媒体窗口管理 ====================

    /**
     * 创建媒体窗口（视频/图片）
     */
    fun createMediaWindow(mediaId: String, mediaType: String, fileName: String, uri: android.net.Uri, contentType: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法创建媒体窗口")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                // 检查是否已存在相同ID的媒体窗口
                if (dataModule.containsWindow(mediaId)) {
                    AppLog.d("媒体窗口已存在: $mediaType (ID: $mediaId)")
                    return@runOnUiThread
                }

                // 创建媒体窗口
                creationModule.createMediaWindow(mediaId, mediaType, fileName, uri, contentType)

                AppLog.d("媒体窗口创建完成: $mediaType (ID: $mediaId, 文件: $fileName)")

            } catch (e: Exception) {
                AppLog.e("创建媒体窗口失败: $mediaType", e)
            }
        }
    }

    // ==================== 文本窗口管理 ====================

    /**
     * 创建文本窗口
     */
    fun createTextWindow(textId: String, textContent: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法创建文本窗口")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                // 检查是否已存在相同ID的文本窗口
                if (dataModule.containsWindow(textId)) {
                    AppLog.d("文本窗口已存在: ID=$textId")
                    return@runOnUiThread
                }

                // 创建文本窗口
                creationModule.createTextWindow(textId, textContent)

                AppLog.d("文本窗口创建完成: ID=$textId, 文本=$textContent")

            } catch (e: Exception) {
                AppLog.e("创建文本窗口失败: ID=$textId", e)
            }
        }
    }

    /**
     * 📝 应用远程文字格式同步
     */
    fun applyRemoteTextFormatSync(connectionId: String, formatData: Map<String, Any>) {
        if (!isInitialized) {
            AppLog.w("WindowSettingsManager未初始化，无法应用远程文字格式同步")
            return
        }

        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                AppLog.d("📝 开始应用远程文字格式同步: $connectionId")

                // 获取对应的文字窗口管理器
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler == null) {
                    AppLog.w("📝 未找到对应的文字窗口: $connectionId")
                    return@runOnUiThread
                }

                // 获取文字窗口管理器
                val textWindowManager = transformHandler.getTextWindowManager()
                if (textWindowManager == null) {
                    AppLog.w("📝 文字窗口管理器不存在: $connectionId")
                    return@runOnUiThread
                }

                // 应用格式数据
                applyFormatDataToTextWindow(textWindowManager, formatData)

                // 🎯 新增：应用窗口变换数据
                applyWindowTransformData(transformHandler, formatData)

                AppLog.d("📝 远程文字格式同步完成: $connectionId")

            } catch (e: Exception) {
                AppLog.e("📝 应用远程文字格式同步失败: $connectionId", e)
            }
        }
    }

    /**
     * 📝 应用格式数据到文字窗口
     */
    private fun applyFormatDataToTextWindow(textWindowManager: com.example.castapp.ui.windowsettings.TextWindowManager, formatData: Map<String, Any>) {
        try {
            val textContent = formatData["textContent"] as? String
            val richTextData = formatData["richTextData"] as? String
            val isBold = formatData["isBold"] as? Boolean ?: false
            val isItalic = formatData["isItalic"] as? Boolean ?: false
            val fontSize = formatData["fontSize"] as? Int ?: 13

            AppLog.d("📝 应用格式数据: 内容长度=${textContent?.length ?: 0}, 富文本=${richTextData != null}, 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")

            // 获取文字窗口视图
            val textWindowView = textWindowManager.getTextWindowView()
            if (textWindowView == null) {
                AppLog.w("📝 文字窗口视图不存在")
                return
            }

            // 如果有富文本数据，优先使用富文本格式
            if (richTextData != null && !textContent.isNullOrEmpty()) {
                try {
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(textWindowView.context)
                    val spannableString = textFormatManager.deserializeSpannableString(textContent, richTextData)
                    textWindowView.setText(spannableString)
                    AppLog.d("📝 富文本格式已应用")
                } catch (e: Exception) {
                    AppLog.e("📝 应用富文本格式失败，使用基本格式", e)
                    applyBasicFormat(textWindowView, textContent, isBold, isItalic, fontSize)
                }
            } else {
                // 使用基本格式
                applyBasicFormat(textWindowView, textContent, isBold, isItalic, fontSize)
            }

            // 🎯 关键修复：应用扩展格式信息（行间距、对齐方式、窗口背景颜色）
            applyExtendedFormatData(textWindowView, formatData)

            // 🎯 关键修复：应用格式数据后保存到本地存储
            saveAppliedFormatData(textWindowManager, formatData)

        } catch (e: Exception) {
            AppLog.e("📝 应用格式数据到文字窗口失败", e)
        }
    }

    /**
     * 🎯 应用窗口变换数据
     */
    private fun applyWindowTransformData(transformHandler: com.example.castapp.ui.windowsettings.TransformHandler, formatData: Map<String, Any>) {
        try {
            val windowTransform = formatData["windowTransform"] as? Map<String, Any>
            if (windowTransform == null) {
                AppLog.d("📝 无窗口变换数据，跳过变换同步")
                return
            }

            AppLog.d("📝 开始应用窗口变换数据: ${windowTransform.keys}")

            // 获取变换数据
            val x = (windowTransform["x"] as? Number)?.toFloat()
            val y = (windowTransform["y"] as? Number)?.toFloat()
            val visualizedWidth = (windowTransform["width"] as? Number)?.toInt()
            val visualizedHeight = (windowTransform["height"] as? Number)?.toInt()
            val remoteControlScale = (windowTransform["remoteControlScale"] as? Number)?.toDouble()
            val scaleX = (windowTransform["scaleX"] as? Number)?.toFloat()
            val scaleY = (windowTransform["scaleY"] as? Number)?.toFloat()
            val rotation = (windowTransform["rotation"] as? Number)?.toFloat()
            val alpha = (windowTransform["alpha"] as? Number)?.toFloat()

            // 🎯 关键修复：遥控端现在发送的是已转换的实际尺寸，接收端直接使用
            val actualWidth = visualizedWidth
            val actualHeight = visualizedHeight

            AppLog.d("📝 窗口变换数据解析:")
            AppLog.d("  位置: ($x, $y)")
            AppLog.d("  🎯 遥控端发送的实际尺寸: ${visualizedWidth}x${visualizedHeight}")
            AppLog.d("  远程控制缩放因子: $remoteControlScale (仅供验证)")
            AppLog.d("  🎯 接收端直接应用的实际窗口尺寸: ${actualWidth}x${actualHeight}")
            AppLog.d("  缩放: ($scaleX, $scaleY)")
            AppLog.d("  旋转: $rotation°")
            AppLog.d("  透明度: $alpha")

            // 应用尺寸变化（如果有）
            if (actualWidth != null && actualHeight != null) {
                val textWindowManager = transformHandler.getTextWindowManager()
                textWindowManager?.setTextWindowSize(actualWidth, actualHeight)

                // 🎯 关键修复：延迟更新边框，确保窗口尺寸完全应用后再更新边框
                transformHandler.post {
                    forceUpdateBorderAfterSizeChange(transformHandler)
                    AppLog.d("📝 边框延迟更新完成")
                }

                AppLog.d("📝 窗口尺寸已应用: ${actualWidth}x${actualHeight}")
                AppLog.d("📝 尺寸应用: 遥控端发送${visualizedWidth}x${visualizedHeight} → 接收端直接应用${actualWidth}x${actualHeight}")
            }

            // 应用位置和变换（如果有）
            if (x != null && y != null && scaleX != null && rotation != null) {
                // 使用平均缩放值（假设scaleX和scaleY相同）
                val scale = scaleX
                transformHandler.setPrecisionTransform(x, y, scale, rotation)
                AppLog.d("📝 窗口位置和变换已应用: 位置=($x, $y), 缩放=$scale, 旋转=$rotation°")
            }

            // 应用透明度（如果有）
            if (alpha != null) {
                // 透明度需要应用到容器视图
                val containerView = transformHandler.getContainerView()
                containerView?.alpha = alpha
                AppLog.d("📝 窗口透明度已应用: $alpha")
            }

            AppLog.d("📝 窗口变换数据应用完成")

        } catch (e: Exception) {
            AppLog.e("📝 应用窗口变换数据失败", e)
        }
    }

    /**
     * 🎯 强制更新边框以匹配窗口尺寸变化
     */
    private fun forceUpdateBorderAfterSizeChange(transformHandler: com.example.castapp.ui.windowsettings.TransformHandler) {
        try {
            AppLog.d("📝 开始强制更新边框以匹配窗口尺寸变化")

            // 方法1：通过TransformHandler的边框更新方法
            transformHandler.updateUnifiedBorderAfterResize()

            // 方法2：如果有边框启用，强制重新应用边框设置
            if (transformHandler.isBorderEnabled()) {
                val currentBorderColor = transformHandler.getBorderColor()
                val currentBorderWidth = transformHandler.getBorderWidth()

                // 🎯 强化修复：先禁用边框，再重新启用，强制完全重建
                transformHandler.setBorderEnabled(false)
                transformHandler.post {
                    transformHandler.setBorderEnabled(true)
                    transformHandler.setBorderColor(currentBorderColor)
                    transformHandler.setBorderWidth(currentBorderWidth)
                    AppLog.d("📝 边框已完全重建: 颜色=${String.format("#%08X", currentBorderColor)}, 宽度=${currentBorderWidth}dp")
                }

                AppLog.d("📝 边框重建流程已启动")
            }

            // 方法3：强制容器重新布局和重绘
            val containerView = transformHandler.getContainerView()
            containerView?.let { view ->
                view.requestLayout()
                view.invalidate()

                // 如果是ViewGroup，也刷新所有子视图
                if (view is android.view.ViewGroup) {
                    for (i in 0 until view.childCount) {
                        val child = view.getChildAt(i)
                        child.requestLayout()
                        child.invalidate()
                    }
                }

                AppLog.d("📝 容器视图已强制重新布局和重绘")
            }

            AppLog.d("📝 边框强制更新完成")

        } catch (e: Exception) {
            AppLog.e("📝 强制更新边框失败", e)
        }
    }

    /**
     * 🎯 关键修复：保存应用的格式数据到本地存储
     */
    private fun saveAppliedFormatData(textWindowManager: com.example.castapp.ui.windowsettings.TextWindowManager, formatData: Map<String, Any>) {
        try {
            val textWindowView = textWindowManager.getTextWindowView()
            if (textWindowView == null) {
                AppLog.w("📝 无法保存格式数据：文字窗口视图不存在")
                return
            }

            // 从formatData中获取connectionId（即textId）
            val connectionId = formatData["connectionId"] as? String
            if (connectionId == null) {
                AppLog.w("📝 无法保存格式数据：缺少connectionId")
                return
            }

            // 保存富文本格式
            val richTextData = formatData["richTextData"] as? String
            if (richTextData != null) {
                val textContent = formatData["textContent"] as? String
                if (!textContent.isNullOrEmpty()) {
                    try {
                        val textFormatManager = com.example.castapp.utils.TextFormatManager(textWindowView.context)
                        val spannableString = textFormatManager.deserializeSpannableString(textContent, richTextData)
                        textFormatManager.saveRichTextFormat(connectionId, spannableString)
                        AppLog.d("📝 富文本格式已保存到本地存储: ID=$connectionId")
                    } catch (e: Exception) {
                        AppLog.e("📝 保存富文本格式失败", e)
                    }
                }
            }

            // 保存扩展格式信息
            val fontName = formatData["fontName"] as? String
            val lineSpacing = when (val spacing = formatData["lineSpacing"]) {
                is Float -> spacing
                is Double -> spacing.toFloat()
                is Int -> spacing.toFloat()
                is Number -> spacing.toFloat()
                else -> 0.0f
            }
            val textAlignment = when (val alignment = formatData["textAlignment"]) {
                is Int -> alignment
                is Long -> alignment.toInt()
                is Float -> alignment.toInt()
                is Double -> alignment.toInt()
                is Number -> alignment.toInt()
                else -> android.view.Gravity.CENTER
            }

            // 保存扩展格式到SharedPreferences
            val sharedPrefs = textWindowView.context.getSharedPreferences("text_format_preferences", android.content.Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                if (!fontName.isNullOrBlank()) {
                    putString("font_name_$connectionId", fontName)
                }
                putFloat("line_spacing_$connectionId", lineSpacing)
                putInt("text_alignment_$connectionId", textAlignment)
                apply()
            }

            AppLog.d("📝 扩展格式信息已保存到本地存储: ID=$connectionId, 字体=$fontName, 行间距=${lineSpacing}dp, 对齐=$textAlignment")

        } catch (e: Exception) {
            AppLog.e("📝 保存应用的格式数据失败", e)
        }
    }

    /**
     * 📝 应用基本格式
     */
    private fun applyBasicFormat(textWindowView: com.example.castapp.ui.view.TextWindowView, textContent: String?, isBold: Boolean, isItalic: Boolean, fontSize: Int) {
        try {
            if (!textContent.isNullOrEmpty()) {
                textWindowView.setTextContent(textContent)
            }
            textWindowView.setBoldEnabled(isBold)
            textWindowView.setItalicEnabled(isItalic)
            textWindowView.setFontSize(fontSize)
            AppLog.d("📝 基本格式已应用: 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")
        } catch (e: Exception) {
            AppLog.e("📝 应用基本格式失败", e)
        }
    }

    /**
     * 🎯 关键修复：应用扩展格式数据（行间距、对齐方式、窗口背景颜色）
     */
    private fun applyExtendedFormatData(textWindowView: com.example.castapp.ui.view.TextWindowView, formatData: Map<String, Any>) {
        try {
            // 🎯 关键修复：应用行间距，处理类型转换问题
            val lineSpacing = when (val spacing = formatData["lineSpacing"]) {
                is Float -> spacing
                is Double -> spacing.toFloat()
                is Int -> spacing.toFloat()
                is Number -> spacing.toFloat()
                else -> 0.0f
            }
            AppLog.d("📝 行间距解析: 原始值=${formatData["lineSpacing"]}, 类型=${formatData["lineSpacing"]?.javaClass?.simpleName}, 解析后=${lineSpacing}dp")
            if (lineSpacing > 0.0f) {
                val lineSpacingExtra = lineSpacing * textWindowView.resources.displayMetrics.density
                textWindowView.setLineSpacing(lineSpacingExtra, 1.0f)
                AppLog.d("📝 行间距已应用: ${lineSpacing}dp (${lineSpacingExtra}px)")
            } else {
                AppLog.d("📝 行间距跳过: 值为${lineSpacing}dp")
            }

            // 🎯 关键修复：应用文本对齐，处理类型转换问题
            val textAlignment = when (val alignment = formatData["textAlignment"]) {
                is Int -> alignment
                is Long -> alignment.toInt()
                is Float -> alignment.toInt()
                is Double -> alignment.toInt()
                is Number -> alignment.toInt()
                else -> null
            }
            AppLog.d("📝 文本对齐解析: 原始值=${formatData["textAlignment"]}, 类型=${formatData["textAlignment"]?.javaClass?.simpleName}, 解析后=$textAlignment")
            if (textAlignment != null) {
                textWindowView.gravity = textAlignment
                AppLog.d("📝 文本对齐已应用: $textAlignment")
            } else {
                AppLog.d("📝 文本对齐跳过: 值为null")
            }

            // 🎯 关键修复：应用窗口背景颜色，处理类型转换问题
            val isWindowColorEnabled = formatData["isWindowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = when (val color = formatData["windowBackgroundColor"]) {
                is Int -> color
                is Long -> color.toInt()
                is Float -> color.toInt()
                is Double -> color.toInt()
                is Number -> color.toInt()
                else -> android.graphics.Color.TRANSPARENT
            }
            AppLog.d("📝 窗口背景颜色解析: 原始值=${formatData["windowBackgroundColor"]}, 类型=${formatData["windowBackgroundColor"]?.javaClass?.simpleName}, 解析后=${String.format("#%08X", windowBackgroundColor)}")
            textWindowView.setWindowBackgroundColor(isWindowColorEnabled, windowBackgroundColor)
            AppLog.d("📝 窗口背景颜色已应用: 启用=$isWindowColorEnabled, 颜色=${String.format("#%08X", windowBackgroundColor)}")

        } catch (e: Exception) {
            AppLog.e("📝 应用扩展格式数据失败", e)
        }
    }

    // ==================== 视频控制处理 ====================

    /**
     * 🎬 处理视频播放开关
     */
    private fun handleVideoPlaySwitch(connectionId: String, isEnabled: Boolean) {
        try {
            val transformHandler = dataModule.getWindowMapping(connectionId)
            if (transformHandler != null) {
                val mediaSurfaceManager = transformHandler.getMediaSurfaceManager()
                mediaSurfaceManager?.setPlayEnabled(isEnabled)
                AppLog.d("🎬 视频播放开关处理完成: $connectionId = $isEnabled")
            } else {
                AppLog.w("🎬 未找到对应的TransformHandler: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("🎬 处理视频播放开关失败: $connectionId", e)
        }
    }

    /**
     * 🎬 处理视频循环次数设置
     */
    private fun handleVideoLoopCount(connectionId: String, loopCount: Int) {
        try {
            val transformHandler = dataModule.getWindowMapping(connectionId)
            if (transformHandler != null) {
                val mediaSurfaceManager = transformHandler.getMediaSurfaceManager()
                mediaSurfaceManager?.setLoopCount(loopCount)
                AppLog.d("🎬 视频循环次数设置完成: $connectionId = $loopCount")
            } else {
                AppLog.w("🎬 未找到对应的TransformHandler: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("🎬 处理视频循环次数失败: $connectionId", e)
        }
    }

    /**
     * 🎬 处理视频音量调整
     */
    private fun handleVideoVolume(connectionId: String, volume: Int) {
        try {
            val transformHandler = dataModule.getWindowMapping(connectionId)
            if (transformHandler != null) {
                val mediaSurfaceManager = transformHandler.getMediaSurfaceManager()
                mediaSurfaceManager?.setVolume(volume)
                AppLog.d("🎬 视频音量调整完成: $connectionId = ${volume}%")
            } else {
                AppLog.w("🎬 未找到对应的TransformHandler: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("🎬 处理视频音量调整失败: $connectionId", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        if (!isInitialized) {
            AppLog.d("CastWindowManager未初始化，无需清理")
            return
        }

        try {
            lifecycleModule.cleanup()
            dataModule.cleanup()

            precisionControlUpdateCallback = null
            transformValueChangeCallback = null

            isInitialized = false
            AppLog.d("CastWindowManager模块化清理完成")
        } catch (e: Exception) {
            AppLog.e("CastWindowManager清理时发生异常", e)
        }
    }

    // 🗑️ 删除状态管理
    private val deletingConnections = mutableSetOf<String>()

    /**
     * 🗑️ 处理窗口删除 - 优化版本
     * 重新设计删除流程，确保时序正确和状态一致性
     */
    private fun handleWindowDelete(connectionId: String) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法删除窗口")
            return
        }

        // 🚀 防重复删除检查
        synchronized(deletingConnections) {
            if (deletingConnections.contains(connectionId)) {
                AppLog.w("【窗口删除】连接 $connectionId 正在删除中，跳过重复删除")
                return
            }
            deletingConnections.add(connectionId)
        }

        try {
            AppLog.d("【窗口删除】开始删除窗口: $connectionId")

            when (connectionId) {
                "front_camera", "rear_camera" -> {
                    handleCameraWindowDelete(connectionId)
                }
                else -> {
                    handleCastWindowDelete(connectionId)
                }
            }

        } catch (e: Exception) {
            AppLog.e("【窗口删除】删除窗口失败: $connectionId", e)
        } finally {
            // 🚀 确保删除状态被清理
            synchronized(deletingConnections) {
                deletingConnections.remove(connectionId)
            }
        }
    }

    /**
     * 🗑️ 处理摄像头窗口删除
     */
    private fun handleCameraWindowDelete(connectionId: String) {
        val cameraName = if (connectionId == "front_camera") "前置摄像头" else "后置摄像头"
        AppLog.d("【窗口删除】删除${cameraName}窗口")

        // 停止摄像头预览并移除窗口
        lifecycleModule.removeWindowForConnection(connectionId)

        AppLog.d("【窗口删除】${cameraName}窗口删除完成")
    }

    /**
     * 🗑️ 处理投屏窗口删除 - 优化时序
     */
    private fun handleCastWindowDelete(connectionId: String) {
        AppLog.d("【窗口删除】删除投屏窗口: $connectionId")

        val activity = dataModule.getCurrentActivity()
        if (activity == null) {
            AppLog.w("【窗口删除】无法获取Activity，仅执行本地窗口移除")
            lifecycleModule.removeWindowForConnection(connectionId)
            return
        }

        try {
            val stateManager = StateManager.getInstance(activity.application)
            val webSocketManager = WebSocketManager.getInstance()

            // 🚀 步骤1：优雅停止MediaCodec（避免强制终止异常）
            AppLog.d("【窗口删除】步骤1: 优雅停止MediaCodec")
            gracefulStopMediaCodec(connectionId)

            // 🚀 步骤2：移除窗口视图
            AppLog.d("【窗口删除】步骤2: 移除窗口视图")
            lifecycleModule.removeWindowForConnection(connectionId)

            // 🚀 步骤3：协调断开WebSocket连接（避免双向冲突）
            AppLog.d("【窗口删除】步骤3: 协调断开WebSocket连接")
            coordinatedWebSocketDisconnect(connectionId, webSocketManager)

            // 🚀 步骤4：清理StateManager状态（确保最后执行）
            AppLog.d("【窗口删除】步骤4: 清理StateManager状态")
            stateManager.removeConnectionById(connectionId)
            AppLog.d("【窗口删除】已从StateManager移除连接: $connectionId")

            AppLog.d("【窗口删除】投屏窗口删除完成: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【窗口删除】清理投屏窗口失败: $connectionId", e)
            // 🚀 异常情况下仍要确保本地窗口被移除
            try {
                lifecycleModule.removeWindowForConnection(connectionId)
            } catch (cleanupException: Exception) {
                AppLog.e("【窗口删除】异常清理本地窗口失败: $connectionId", cleanupException)
            }
        }
    }

    /**
     * 🚀 优雅停止MediaCodec，避免强制终止异常
     */
    private fun gracefulStopMediaCodec(connectionId: String) {
        try {
            // 通知ReceivingService优雅停止指定连接的MediaCodec
            ReceivingService.gracefulStopDecoderForConnection(connectionId)
            AppLog.d("【窗口删除】已通知优雅停止MediaCodec: $connectionId")

            // 给MediaCodec一点时间完成清理
            Thread.sleep(100)
        } catch (e: Exception) {
            AppLog.w("【窗口删除】优雅停止MediaCodec失败，将继续删除流程: $connectionId", e)
        }
    }

    /**
     * 🚀 协调WebSocket断开，避免双向冲突
     */
    private fun coordinatedWebSocketDisconnect(connectionId: String, webSocketManager: WebSocketManager) {
        try {
            // 先断开客户端连接（发送端）
            webSocketManager.forceDisconnectConnection(connectionId)
            AppLog.d("【窗口删除】已断开WebSocket客户端连接: $connectionId")

            // 给客户端一点时间处理断开
            Thread.sleep(100)

            // 再断开服务器端连接（接收端）
            try {
                ReceivingService.disconnectSpecificConnection(connectionId)
                AppLog.d("【窗口删除】已断开WebSocket服务器端连接: $connectionId")
            } catch (e: Exception) {
                AppLog.e("【窗口删除】断开WebSocket服务器端连接失败: $connectionId", e)
            }

        } catch (e: Exception) {
            AppLog.e("【窗口删除】协调WebSocket断开失败: $connectionId", e)
        }
    }

    // ==================== 🎯 横屏模式控制 ====================

    // 🎯 横屏模式状态本地存储（用于UI显示和布局保存）
    private val landscapeModeStates = mutableMapOf<String, Boolean>()

    /**
     * 处理横屏模式开关变化
     */
    fun handleLandscapeModeSwitch(connectionId: String, isEnabled: Boolean) {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法处理横屏模式开关")
            return
        }

        try {
            AppLog.d("🎯 【横屏模式】连接 $connectionId 横屏模式开关变更为: $isEnabled")

            // 🎯 更新本地状态存储（用于UI显示和布局保存）
            landscapeModeStates[connectionId] = isEnabled
            AppLog.d("🎯 【横屏模式】本地状态已更新: $connectionId -> $isEnabled")

            // 通过WebSocket发送横屏控制消息给发送端
            sendLandscapeModeControlMessage(connectionId, isEnabled)

            // 🎯 刷新窗口管理对话框以显示最新状态
            refreshWindowManagerDialog()

        } catch (e: Exception) {
            AppLog.e("🎯 【横屏模式】处理横屏模式开关失败: $connectionId", e)
        }
    }

    /**
     * 🎯 获取横屏模式状态
     */
    fun getLandscapeModeEnabled(connectionId: String): Boolean {
        return landscapeModeStates[connectionId] ?: false
    }

    /**
     * 🎯 仅设置横屏模式状态（不发送控制消息）
     * 用于首次投屏时的状态恢复，避免时序问题
     */
    fun setLandscapeModeStateOnly(connectionId: String, isEnabled: Boolean) {
        landscapeModeStates[connectionId] = isEnabled
        AppLog.d("🎯 【横屏模式】仅设置本地状态: $connectionId -> $isEnabled")
    }

    /**
     * 🎯 发送横屏模式控制消息（公共方法）
     * 用于延迟发送控制消息
     */
    fun sendLandscapeModeControlMessagePublic(connectionId: String, isEnabled: Boolean) {
        sendLandscapeModeControlMessage(connectionId, isEnabled)
    }

    /**
     * 🎯 清除横屏模式状态（连接断开时调用）
     */
    fun clearLandscapeModeState(connectionId: String) {
        landscapeModeStates.remove(connectionId)
        AppLog.d("🎯 【横屏模式】已清除状态: $connectionId")
    }

    /**
     * 发送横屏模式控制消息给发送端
     * 🎯 修复：通过接收端WebSocket服务器发送消息给发送端
     */
    private fun sendLandscapeModeControlMessage(connectionId: String, isEnabled: Boolean) {
        try {
            AppLog.d("🎯 【横屏模式】准备发送横屏控制消息: $connectionId -> $isEnabled")

            // 🎯 方法1：通过ReceivingService的WebSocket服务器发送消息
            val webSocketServer = ReceivingService.getWebSocketServer()
            if (webSocketServer != null) {
                AppLog.d("🎯 【横屏模式】通过接收端WebSocket服务器发送横屏控制消息")
                val message = com.example.castapp.websocket.ControlMessage.createLandscapeModeControl(connectionId, isEnabled)
                val success = webSocketServer.sendMessageToClient(connectionId, message)

                if (success) {
                    AppLog.d("🎯 【横屏模式】接收端WebSocket控制消息发送成功: $connectionId -> $isEnabled")
                } else {
                    AppLog.w("🎯 【横屏模式】接收端WebSocket控制消息发送失败: $connectionId")
                    AppLog.d("🎯 【横屏模式】活跃连接列表: ${webSocketServer.getActiveConnectionIds()}")
                }
                return
            }

            // 🎯 方法2：尝试通过远程控制服务器发送
            try {
                val remoteReceiverControlServer = com.example.castapp.remote.RemoteReceiverControlServer.getInstance()
                AppLog.d("🎯 【横屏模式】尝试通过远程控制服务器发送横屏控制消息")
                val message = com.example.castapp.websocket.ControlMessage.createLandscapeModeControl(connectionId, isEnabled)
                remoteReceiverControlServer.broadcastMessage(message)
                AppLog.d("🎯 【横屏模式】远程控制服务器广播消息完成: $connectionId -> $isEnabled")
                return
            } catch (e: Exception) {
                AppLog.w("🎯 【横屏模式】远程控制服务器发送失败", e)
            }

            // 🎯 如果所有方法都失败，记录调试信息
            AppLog.w("🎯 【横屏模式】无法找到有效的WebSocket服务器: $connectionId")

        } catch (e: Exception) {
            AppLog.e("🎯 【横屏模式】发送控制消息失败: $connectionId", e)
        }
    }

    // ========== 📝 文字内容获取功能 ==========

    /**
     * 📝 获取所有文字窗口的内容
     * @return 文字内容数据列表
     */
    fun getAllTextWindowContents(): List<Map<String, Any>> {
        if (!isInitialized) {
            AppLog.w("CastWindowManager未初始化，无法获取文字内容")
            return emptyList()
        }

        try {
            val textContentDataList = mutableListOf<Map<String, Any>>()
            val windowMappings = dataModule.getAllWindowMappings()

            windowMappings.forEach { (connectionId, windowContainer) ->
                try {
                    // 检查是否为文字窗口，使用公开的getter方法
                    val textWindowManager = windowContainer.getTextWindowManager()
                    if (textWindowManager != null) {
                        // 🎯 修复：获取完整的文字格式信息（包含富文本格式）
                        val completeFormatInfo = textWindowManager.getCompleteTextFormatInfo()

                        // 确保connectionId正确，并转换为正确的类型
                        val textContentData = mutableMapOf<String, Any>()
                        completeFormatInfo.forEach { (key, value) ->
                            if (value != null) {
                                textContentData[key] = value
                            }
                        }
                        textContentData["connectionId"] = connectionId

                        textContentDataList.add(textContentData)

                        val textContent = completeFormatInfo["textContent"] as? String ?: ""
                        val hasRichText = completeFormatInfo.containsKey("richTextData")
                        AppLog.d("📝 获取文字窗口完整信息: $connectionId, 内容: $textContent, 富文本: $hasRichText")
                    }
                } catch (e: Exception) {
                    AppLog.e("📝 获取文字窗口内容失败: $connectionId", e)
                }
            }

            AppLog.d("📝 文字内容获取完成，共 ${textContentDataList.size} 个文字窗口")
            return textContentDataList

        } catch (e: Exception) {
            AppLog.e("📝 获取所有文字窗口内容失败", e)
            return emptyList()
        }
    }

}
